package com.wbgame.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.master.OppoViolationRecordMapper;
import com.wbgame.mapper.master.PlatformDataMapper;
import com.wbgame.pojo.OppoViolationRecord;
import com.wbgame.pojo.OppoViolationRecordDTO;
import com.wbgame.pojo.OppoViolationRecordVo;
import com.wbgame.pojo.adv2.ExtendAdsidVo;
import com.wbgame.pojo.adv2.PlatformAppInfoVo;
import com.wbgame.service.OppoViolationRecordService;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.FeishuUtils;
import com.wbgame.utils.StringUtils;
import com.wbgame.utils.UserPermissionsUtils;
import com.wbgame.utils.tool.Base64;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER;

/**
 * <AUTHOR>
 * @Description oppo违规记录业务实现层
 * @Date 2025/03/06 16:00
 */
@Service
public class OppoViolationRecordServiceImpl implements OppoViolationRecordService {
    Logger logger = LoggerFactory.getLogger(OppoViolationRecordServiceImpl.class);

    @Autowired
    private OppoViolationRecordMapper violationRecordMapper;

    @Autowired
    private DnwxBiAdtMapper dnwxBiAdtMapper;
    @Autowired
    private Adv2Mapper adv2Mapper;

    @Autowired
    private PlatformDataMapper platformDataMapper;

    @Autowired
    private UserPermissionsUtils userPermissionsUtils;


    //api调用频率限定
    private static final RateLimiter mailboxesRateLimiter = RateLimiter.create(10.0); // 每秒最多 10 次，查询用户邮箱列表
    private static final RateLimiter messageRateLimiter = RateLimiter.create(100 / 60.0); //每秒最多 100/60 次，查询用户邮箱详情api

    private static final int MAX_RETRIES = 3; // 最大重试次数


    private static final Set<String> violationTypeSet = Sets.newHashSet("排查通知", "异常排查结果通知", "排查逾期通知", "广告违规警告");

    /**
     * 绑定模式
     */
    public static final Map<String, String> BIDDING_MODE = new HashMap<String, String>() {{
        put("0", "否");
        put("1", "c2sbidding");
        put("2", "s2sbidding");
    }};
    /**
     * 1-违规，2-质量分,0-其他
     */
    public static final Map<String, String> VIOLATION_TYPE = new HashMap<String, String>() {{
        put("0", "其他");
        put("1", "违规");
        put("2", "质量分");
    }};
    /**
     * 广告使用类型
     */
    public static final Map<String, String> AD_USAGE_TYPE = new HashMap<String, String>() {{
        put("splash", "开屏");
        put("plaque", "插屏");
        put("video", "视频");
        put("msg", "信息流");
        put("banner", "banner");
        put("icon", "icon");
        put("draw", "draw信息流");
        put("box", "box");
    }};
    public static final Map<String, String> SKD_AD_TYPE = new HashMap<String, String>() {{
        put("splash", "开屏");
        put("natSplash", "原生开屏");
        put("banner", "普通banner/模板");
        put("natBanner", "banner自渲染");
        put("yuans", "信息流模板");
        put("msg", "信息流自渲染");
        put("plaque", "普通插屏/模板");
        put("natPlaque", "自渲染插屏");
        put("plaqueVideo", "插屏视频");
        put("video", "视频");
        put("natVideo", "视频自渲染");
        put("icon", "icon");
        put("draw", "draw信息流");
    }};

    private static final List<String> validEmailTypes = Lists.newArrayList("OPPO联盟媒体结算通知", "质量分额外补贴金额通知", "流量异常");

    private static final String APP_ID = "cli_a196449bb7b8d013";
    private static final String APP_SECRET = "nIcrRWrH1p2sfilZ6W7JaeNoyGm6Dsqe";


    /**
     * 根据时间拉取指定账号的邮箱收件数据
     *
     * @param day 天数
     * @param email 邮件
     * @param prompts 关键词数组
     * @param username 操作人
     */
    @Override
    public List<OppoViolationRecord> fetchEmailsOfPrompts(int day, String email, String[] prompts,String username) {
        logger.info("拉取指定账号的邮箱收件数据 开始...");
        long beginTime = System.currentTimeMillis();
        //获取访问权限token--使用 系统测试环境应用token
        String tenant_access_token = FeishuUtils.getTenantAccessTokenTwo(APP_ID, APP_SECRET);
        //1. 获取飞书邮箱收件箱列表数据
        //分页大小:限制 1-20
        int pageSize = 20;
        //分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
        String pageToken = null;
        long startTime = 0;
        if (day >= 0) {
            startTime = LocalDate.now().minusDays(day).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        }
        StringBuilder failMsg = new StringBuilder();
        List<OppoViolationRecord> violationRecordList = new ArrayList<>();
        outer:
        while (true) {
            //查询当前账号邮箱列表数据
            String userMailboxes = queryUserMailboxesWithRetry(email, tenant_access_token, pageSize, pageToken);
            logger.info("userMailboxes: {}", userMailboxes);
            if (StringUtils.isEmpty(userMailboxes)) {
                //程序出现异常，进行飞书通知告警操作
                logger.error("获取邮箱列表异常,请及时处理！！");
                failMsg.append("获取邮箱列表异常,请及时处理！！");
                //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,请及时处理！！","all");
                break;
            }
            JSONObject emailListObj = JSON.parseObject(userMailboxes);
            //code 错误码，非 0 表示失败
            Integer code = emailListObj.getInteger("code");
            if (code == null || code != 0) {
                //程序出现异常，进行飞书通知告警操作
                logger.error("获取邮箱列表异常,异常原因：" + emailListObj.getString("msg") + ",请及时处理！！");
                failMsg.append("获取邮箱列表异常,异常原因：").append(emailListObj.getString("msg")).append(",请及时处理！！");
                //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,异常原因："+emailListObj.getString("msg")+",请及时处理！！","all");
                break;
            }
            //获取邮件列表数据
            JSONObject data = emailListObj.getJSONObject("data");
            if (ObjectUtils.isEmpty(data)) break;
            JSONArray items = data.getJSONArray("items");
            //对邮件分别处理操作
            for (Object item : items) {
                //邮件id
                String message_id = (String) item;
                //获取邮件详情数据
                String emailMsg = queryUserMessageWithRetry(email, tenant_access_token, message_id);
                logger.info("\temailMsg: {}", emailMsg);
                JSONObject emailMsgObj = JSON.parseObject(emailMsg);
                //返回结果校验
                if (emailMsgObj == null) {
                    //程序出现异常，进行飞书通知告警操作
                    logger.error("获取邮箱详情数据异常,请及时处理！！");
                    //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,异常原因："+emailListObj.getString("msg")+",请及时处理！！","all");
                    continue;
                }
                Integer emailMsgCode = emailMsgObj.getInteger("code");
                if (emailMsgCode == null || 0 != emailMsgCode) {
                    //程序出现异常，进行飞书通知告警操作
                    logger.error("获取邮箱列表异常,异常原因：" + emailMsgObj.getString("msg") + ",请及时处理！！");
                    //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,异常原因："+emailListObj.getString("msg")+",请及时处理！！","all");
                    continue;
                }
                JSONObject emailMsgData = emailMsgObj.getJSONObject("data");
                if (emailMsgData == null || !emailMsgData.containsKey("message")) {
                    //程序出现异常，进行飞书通知告警操作
                    logger.error("获取邮箱列表异常,异常原因：" + emailMsgObj.getString("msg") + ",请及时处理！！");
                    //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,异常原因："+emailListObj.getString("msg")+",请及时处理！！","all");
                    continue;
                }
                JSONObject message = emailMsgData.getJSONObject("message");
                logger.info("\t\tmessage: {}", message);

                long internalDate = Long.parseLong(message.getString("internal_date"));
                if (internalDate < startTime) {
                    //不在获取的时间内，直接返回
                    break outer;
                }
                String subject = removeEmoji(message.getString("subject"));
                //根据邮件标题过滤
                logger.info("\t\tsubject: {},noneMatch: {}", subject, ((Arrays.asList(prompts)).stream().noneMatch(subject::contains)));

                if (StringUtils.isEmpty(subject) || (Arrays.asList(prompts)).stream().noneMatch(subject::contains)) {
                    //不符合拉取的邮件条件
                    continue;
                }
                String bodyPlainText = message.getString("body_plain_text");
                if (!StringUtils.isEmpty(bodyPlainText)) {
                    String replaced = bodyPlainText.replaceAll("-", "+").replaceAll("_", "/");
                    byte[] bytes = Base64.decodeBase64(replaced.getBytes());
                    bodyPlainText = new String(bytes);
                }
                //封装处理邮件内容
                OppoViolationRecord info =new OppoViolationRecord();
                info.setSubject(subject);
                info.setMessage_id(message.getString("message_id"));
                info.setViolation_date(message.getString("internal_date"));
                info.setContent(bodyPlainText);
                logger.info("\t\tinfo: {}", info);
                violationRecordList.add(info);
            }
            //是否有下一页数据
            boolean hasMore = data.getBooleanValue("has_more");
            if (!hasMore) break;
            //获取下一页标识
            pageToken = data.getString("page_token");
        }
        logger.info("violationRecordList :{}", JSON.toJSONString(violationRecordList));

        logger.info("拉取指定账号的邮箱收件数据 结束...");
        return violationRecordList;
    }

    /**
     * 根据时间拉取指定账号的邮箱收件数据
     *
     * @param day
     * @param email
     */
    @Override
    @Async
    public void fetchEmails(int day, String email, String username) {
        logger.info("拉取oppo违规数据开始");
        long beginTime = System.currentTimeMillis();
        //获取访问权限token--使用 系统测试环境应用token
        String tenant_access_token = FeishuUtils.getTenantAccessTokenTwo(APP_ID, APP_SECRET);
        //1. 获取飞书邮箱收件箱列表数据
        //分页大小:限制 1-20
        int pageSize = 20;
        //分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
        String pageToken = null;
        long startTime = 0;
        if (day >= 0) {
            startTime = LocalDate.now().minusDays(day).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        }
        StringBuilder failMsg = new StringBuilder();
        List<OppoViolationRecord> violationRecordList = new ArrayList<>();
        outer:
        while (true) {
            //查询当前账号邮箱列表数据
            String userMailboxes = queryUserMailboxesWithRetry(email, tenant_access_token, pageSize, pageToken);
            logger.info("userMailboxes: {}", userMailboxes);
            if (StringUtils.isEmpty(userMailboxes)) {
                //程序出现异常，进行飞书通知告警操作
                logger.error("获取邮箱列表异常,请及时处理！！");
                failMsg.append("获取邮箱列表异常,请及时处理！！");
                //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,请及时处理！！","all");
                break;
            }
            JSONObject emailListObj = JSON.parseObject(userMailboxes);
            //code 错误码，非 0 表示失败
            Integer code = emailListObj.getInteger("code");
            if (code == null || code != 0) {
                //程序出现异常，进行飞书通知告警操作
                logger.error("获取邮箱列表异常,异常原因：" + emailListObj.getString("msg") + ",请及时处理！！");
                failMsg.append("获取邮箱列表异常,异常原因：").append(emailListObj.getString("msg")).append(",请及时处理！！");
                //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,异常原因："+emailListObj.getString("msg")+",请及时处理！！","all");
                break;
            }
            //获取邮件列表数据
            JSONObject data = emailListObj.getJSONObject("data");
            if (ObjectUtils.isEmpty(data)) break;
            JSONArray items = data.getJSONArray("items");
            //对邮件分别处理操作
            for (Object item : items) {
                //邮件id
                String message_id = (String) item;
                //获取邮件详情数据
                String emailMsg = queryUserMessageWithRetry(email, tenant_access_token, message_id);
                logger.info("\temailMsg: {}", emailMsg);
                JSONObject emailMsgObj = JSON.parseObject(emailMsg);
                //返回结果校验
                if (emailMsgObj == null) {
                    //程序出现异常，进行飞书通知告警操作
                    logger.error("获取邮箱详情数据异常,请及时处理！！");
                    //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,异常原因："+emailListObj.getString("msg")+",请及时处理！！","all");
                    continue;
                }
                Integer emailMsgCode = emailMsgObj.getInteger("code");
                if (emailMsgCode == null || 0 != emailMsgCode) {
                    //程序出现异常，进行飞书通知告警操作
                    logger.error("获取邮箱列表异常,异常原因：" + emailMsgObj.getString("msg") + ",请及时处理！！");
                    //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,异常原因："+emailListObj.getString("msg")+",请及时处理！！","all");
                    continue;
                }
                JSONObject emailMsgData = emailMsgObj.getJSONObject("data");
                if (emailMsgData == null || !emailMsgData.containsKey("message")) {
                    //程序出现异常，进行飞书通知告警操作
                    logger.error("获取邮箱列表异常,异常原因：" + emailMsgObj.getString("msg") + ",请及时处理！！");
                    //FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","获取邮箱列表异常,异常原因："+emailListObj.getString("msg")+",请及时处理！！","all");
                    continue;
                }
                JSONObject message = emailMsgData.getJSONObject("message");
                long internalDate = Long.parseLong(message.getString("internal_date"));
                if (internalDate < startTime) {
                    //不在获取的时间内，直接返回
                    break outer;
                }
                String subject = removeEmoji(message.getString("subject"));
                //根据邮件标题过滤
                logger.info("\tsubject: {},flag: {}", subject, (!subject.contains("告警")));

//                if (StringUtils.isEmpty(subject) || validEmailTypes.stream().noneMatch(subject::contains)) {
//                    //不符合拉取的邮件条件
//                    continue;
//                }
                String bodyPlainText = message.getString("body_plain_text");
                if (!StringUtils.isEmpty(bodyPlainText)) {
                    String replaced = bodyPlainText.replaceAll("-", "+").replaceAll("_", "/");
                    byte[] bytes = Base64.decodeBase64(replaced.getBytes());
                    bodyPlainText = new String(bytes);
                }
                OppoViolationRecord info =new OppoViolationRecord();
                info.setSubject(subject);
                info.setMessage_id(message.getString("message_id"));
                info.setViolation_date(new Date(internalDate).toString());
                info.setContent(bodyPlainText);
                violationRecordList.add(info);

                //封装处理邮件内容
                List<OppoViolationRecord> recordList = transEmailMessageData(message);
                if (!CollectionUtils.isEmpty(recordList)) {
                    violationRecordList.addAll(recordList);
                }
            }
            //是否有下一页数据
            boolean hasMore = data.getBooleanValue("has_more");
            if (!hasMore) break;
            //获取下一页标识
            pageToken = data.getString("page_token");
        }
        logger.info("violationRecordList :{}", JSON.toJSONString(violationRecordList));
        if(true){return;}
        if (!CollectionUtils.isEmpty(violationRecordList)) {
            // 通过渠道产品id关联配置，广告源表，变现平台明细表 拉取并补充数据
            fetchPlatformData(violationRecordList);
            //新增数据
            List<List<OppoViolationRecord>> partition = Lists.partition(violationRecordList, 5000);
            partition.forEach(data -> violationRecordMapper.batchInsert(data));
        }
        long endTime = System.currentTimeMillis();
        if (!StringUtils.isEmpty(username)) {
            //飞书通知 OPPO站内信数据同步完成
            String msg = String.format("OPPO站内信数据同步完成,耗时%dms", endTime - beginTime);
            if (failMsg.length() != 0) {
                msg += "，拉取存在异常，" + failMsg;
            }
            FeishuUtils.sendFeiShu(msg, username);
        }
        logger.info("拉取oppo违规数据结束");
    }


    @Override
    public Result<String> fetchPlatformData(String startDate, String endDate) {
        OppoViolationRecordDTO oppoViolationRecordDTO = new OppoViolationRecordDTO();
        oppoViolationRecordDTO.setStartTime(startDate);
        oppoViolationRecordDTO.setEndTime(endDate);
        List<OppoViolationRecord> recordList = violationRecordMapper.queryList(oppoViolationRecordDTO);
        if (!CollectionUtils.isEmpty(recordList)) {
            fetchPlatformData(recordList);
            violationRecordMapper.batchInsert(recordList);
        }
        return ResultUtils.success();
    }

    private void fetchPlatformData(List<OppoViolationRecord> violationRecordList) {

        //获取广告位id
        List<String> sdkCodeList = violationRecordList.stream().map(OppoViolationRecord::getAdsense_id).filter(data -> !StringUtils.isEmpty(data)).collect(Collectors.toList());
        //获取广告源表数据
        if (!CollectionUtils.isEmpty(sdkCodeList)) {
            List<ExtendAdsidVo> extendAdsidVos = adv2Mapper.selectExtendAdsidByPubcode(sdkCodeList);
            Map<String, ExtendAdsidVo> adsidVoMap = extendAdsidVos.stream().filter(data -> "oppo".equals(data.getAgent())).collect(Collectors.toMap(ExtendAdsidVo::getSdk_code, Function.identity(), (data1, data2) -> data1));
            for (OppoViolationRecord record : violationRecordList) {
                String adsenseId = record.getAdsense_id();
                if (!StringUtils.isEmpty(adsenseId) && adsidVoMap.containsKey(adsenseId)) {
                    //封装数据
                    ExtendAdsidVo adsidVo = adsidVoMap.get(adsenseId);
                    record.setTappid(adsidVo.getSdk_appid());
                    record.setAdsid(adsidVo.getAdsid());
                    record.setBidding_mode(adsidVo.getBidding());
                    record.setOpen_type(adsidVo.getOpen_type());
                    record.setSdk_adtype(adsidVo.getSdk_adtype());
                }
            }
        }
        //根据媒体id获取渠道产品id配置表数据
        List<String> tappidList = violationRecordList.stream().map(OppoViolationRecord::getTappid).filter(data -> !StringUtils.isEmpty(data)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tappidList)) {
            //根据tappid获取数据
            List<PlatformAppInfoVo> appInfoVoList = platformDataMapper.queryPlatformAppInfoList(tappidList, "oppo");
            Map<String, PlatformAppInfoVo> tappidMap = appInfoVoList.stream().collect(Collectors.toMap(PlatformAppInfoVo::getTappid, Function.identity(), (data1, data2) -> data1));
            for (OppoViolationRecord record : violationRecordList) {
                //封装数据
                String tappid = record.getTappid();
                if (!StringUtils.isEmpty(tappid) && tappidMap.containsKey(tappid)) {
                    PlatformAppInfoVo appInfoVo = tappidMap.get(tappid);
                    record.setAppid(appInfoVo.getAppid());
                    record.setCompany(appInfoVo.getCname());
                    record.setAppname(appInfoVo.getAppname());
                    record.setOnline_name(appInfoVo.getTappname());
                    record.setChannel(appInfoVo.getChannel());
                    record.setPackage_name(appInfoVo.getPackagename());
                }
            }
        }

        //根据消息日期分组
        Map<String, List<OppoViolationRecord>> collect = violationRecordList.stream().collect(Collectors.groupingBy(data -> DateUtil.dateToStr(data.getTdate(), DateUtil.DATE_A)));

        for (Map.Entry<String, List<OppoViolationRecord>> entry : collect.entrySet()) {
            String tdate = entry.getKey();
            List<OppoViolationRecord> recordList = entry.getValue();

            DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");
            DateTime dateTime = DateTime.parse(tdate, formatter);
            //获取最近一月的起始日期
            String startDate = dateTime.minusDays(30).toString(formatter);
            String endDate = dateTime.minusDays(1).toString(formatter);
            //封装查询参数
            List<String> sameDaySdkCodeList = recordList.stream().map(OppoViolationRecord::getAdsense_id).filter(data -> !StringUtils.isEmpty(data)).collect(Collectors.toList());
            List<String> sameDayMediaIdList = recordList.stream().map(OppoViolationRecord::getTappid).filter(data -> !StringUtils.isEmpty(data)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sameDaySdkCodeList)) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("groups", Collections.singletonList("sdk_code"));
                paramMap.put("start_date", startDate);
                paramMap.put("end_date", endDate);
                paramMap.put("agent", "oppo");
                //变现平台明细数据查询
                List<Map<String, Object>> dnChaCashTotal = dnwxBiAdtMapper.selectDnChaCashTotal(paramMap);
                Map<String, Map<String, Object>> placementIdMap = dnChaCashTotal.stream().collect(Collectors.toMap(data -> data.get("sdk_code") + "", Function.identity(), (k1, k2) -> k1));
                for (OppoViolationRecord record : recordList) {
                    String adsenseId = record.getAdsense_id();
                    if (!StringUtils.isEmpty(adsenseId) && placementIdMap.containsKey(adsenseId)) {
                        Map<String, Object> map = placementIdMap.get(adsenseId);
                        //封装收入，展示，点击
                        record.setLast_month_revenue(map.get("revenue") + "");
                        record.setLast_month_show(map.get("pv") + "");
                        record.setLast_month_click(map.get("click") + "");
                    }
                }
            }
            if (!CollectionUtils.isEmpty(sameDayMediaIdList)) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("groups", Collections.singletonList("sdk_appid"));
                paramMap.put("start_date", startDate);
                paramMap.put("end_date", endDate);
                paramMap.put("agent", "oppo");
                //变现平台明细数据查询
                List<Map<String, Object>> dnChaCashTotal = dnwxBiAdtMapper.selectDnChaCashTotal(paramMap);
                Map<String, Map<String, Object>> mediaIdMap = dnChaCashTotal.stream().collect(Collectors.toMap(data -> data.get("sdk_appid") + "", Function.identity(), (k1, k2) -> k1));
                for (OppoViolationRecord record : recordList) {
                    String adsenseId = record.getAdsense_id();
                    String tappid = record.getTappid();
                    if (StringUtils.isEmpty(adsenseId) && !StringUtils.isEmpty(tappid) && mediaIdMap.containsKey(tappid)) {
                        Map<String, Object> map = mediaIdMap.get(tappid);
                        //封装收入，展示，点击
                        record.setLast_month_revenue(map.get("revenue") + "");
                        record.setLast_month_show(map.get("pv") + "");
                        record.setLast_month_click(map.get("click") + "");
                    }
                }
            }
        }
    }

    private String queryUserMessageWithRetry(String email, String tenant_access_token, String message_id) {
        //接口调用频率限定
        messageRateLimiter.acquire();
        int attempt = 0;
        String userMailboxes = null;
        while (attempt < MAX_RETRIES) {
            try {
                attempt++;
                // 调用 API 并返回数据
                userMailboxes = FeishuUtils.queryUserMessage(email, tenant_access_token, message_id);
                JSONObject emailListObj = JSON.parseObject(userMailboxes);
                if (!StringUtils.isEmpty(userMailboxes) && emailListObj.containsKey("code") && 0 == emailListObj.getInteger("code")) {
                    //返回成功数据
                    return userMailboxes;
                }
            } catch (Exception e) {
                logger.error("查询用户邮箱详情apid 调用失败 (尝试 " + attempt + " 次): " + e.getMessage());
            }
            if (attempt == MAX_RETRIES) {
                logger.error("查询用户邮件详情 API 重试失败，放弃请求");

                return userMailboxes; // 失败后返回默认值
            }
            try {
                Thread.sleep(500); // 失败后等待 500ms 再重试
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        }
        return userMailboxes;
    }

    private String queryUserMailboxesWithRetry(String email, String tenant_access_token, int pageSize, String pageToken) {
        //接口调用频率限定
        mailboxesRateLimiter.acquire();
        int attempt = 0;
        String userMailboxes = null;
        while (attempt < MAX_RETRIES) {
            try {
                attempt++;
                // 调用 API 并返回数据
                userMailboxes = FeishuUtils.queryUserMailboxes(email, tenant_access_token, pageSize, pageToken);
                JSONObject emailListObj = JSON.parseObject(userMailboxes);
                if (!StringUtils.isEmpty(userMailboxes) && emailListObj.containsKey("code") && 0 == emailListObj.getInteger("code")) {
                    //返回成功数据
                    return userMailboxes;
                }
            } catch (Exception e) {
                logger.error("查询用户邮箱列表 api 调用失败 (尝试 " + attempt + " 次): " + e.getMessage());
            }
            if (attempt == MAX_RETRIES) {
                logger.error("查询用户邮箱列表 API 重试失败，放弃请求");
                return userMailboxes; // 失败后返回默认值
            }
            try {
                Thread.sleep(500); // 失败后等待 500ms 再重试
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        }
        return userMailboxes;
    }

    /**
     * 邮件内容转换
     *
     * @param message 邮件信息
     * @return 转换结果
     */
    private List<OppoViolationRecord> transEmailMessageData(JSONObject message) {

        List<OppoViolationRecord> recordList = new ArrayList<>();

        //创建/收/发信时间（毫秒）
        String internalDate = message.getString("internal_date");

        //基础字段封装
        OppoViolationRecord violationRecord = new OppoViolationRecord();
        //封装平台
        violationRecord.setPlatform("oppo");
        // 封装其他额外的字段
        //获取标题
        String subject = removeEmoji(message.getString("subject"));
        violationRecord.setSubject(subject);
        //邮件id
        String messageId = message.getString("message_id");
        violationRecord.setMessage_id(messageId);
        //base64Url解密
        String bodyHtml = message.getString("body_html");
        if (!StringUtils.isEmpty(bodyHtml)) {
            String replaced = bodyHtml.replaceAll("-", "+").replaceAll("_", "/");
            byte[] bytes = Base64.decodeBase64(replaced.getBytes());
            violationRecord.setContent(new String(bytes));
        }
        String bodyPlainText = message.getString("body_plain_text");
        if (!StringUtils.isEmpty(bodyPlainText)) {
            String replaced = bodyPlainText.replaceAll("-", "+").replaceAll("_", "/");
            byte[] bytes = Base64.decodeBase64(replaced.getBytes());
            violationRecord.setViolation_content(removeEmoji(new String(bytes)));
        }
        violationRecord.setTdate(new Date(Long.parseLong(internalDate)));
        //赋值分类 1-违规，2-质量分,0-其他
        boolean anyMatch = violationTypeSet.stream().anyMatch(subject::contains);
        if (anyMatch) {
            //赋值类型为违规
            violationRecord.setType("1");
        } else {
            //不符合条件的归为其他类型
            violationRecord.setType(subject.contains("质量分") ? "2" : "0");
        }
        //  邮件内容数据解析
        //获取纯文本邮件内容
        String violationContent = violationRecord.getViolation_content();
        String htmlContent = violationRecord.getContent();

        //对处理日期数据截取
        // 定义正则表达式
        String dateRegex = "(\\d+)\\s*(个工作)?日内";
        Pattern datePattern = Pattern.compile(dateRegex);
        Matcher dateMatcher = datePattern.matcher(violationContent);
        // 提取并输出匹配的时间范围
        if (dateMatcher.find()) {
            String timeRange = dateMatcher.group(0); // 完整匹配的字符串
            violationRecord.setHandle_time(timeRange);
        }
        String emailMainKey = "";
        if (subject.contains("OPPO联盟媒体结算通知")) {
            //OPPO联盟媒体结算通知 提取邮件关键字
            emailMainKey = "对象因申诉失败/申诉逾期，平台将在结算时扣除相应的广告收益";
            Pattern pattern = Pattern.compile("(该媒体\\d+月有延迟结算的金额，将不再补结)");
            Matcher matcher = pattern.matcher(violationContent);
            if (matcher.find()) {
                emailMainKey += "," + matcher.group(1);
            }
            violationRecord.setViolation_content(emailMainKey);
            //媒体ID：30693093 预估核减金额（元）：3.05
            //媒体ID 广告位ID 预估核减金额(元) 31793739 1628892 121.64 31793739 1628895 115.13
            //媒体ID 广告位ID 处罚金额(元) 32631189 1985986 1138.41 32631189 1985988 465.11 32631189 1985972 449.23 32631189 1985971 255.58 32631189 1985973 253.81 32631189 1985970 204.21 32631189 1985974 203.98
            //媒体结算通知:
            // 正则匹配 "媒体ID：数字" 和 "预估核减金额（元）：数字"
            Pattern settledPattern1 = Pattern.compile("媒体ID[:：](\\d+)\\s+预估核减金额（元）[:：]([\\d.]+)");
            Matcher settledMatcher1 = settledPattern1.matcher(violationContent);
            if (settledMatcher1.find()) {
                String mediaId = settledMatcher1.group(1);
                String estimatedReduction = settledMatcher1.group(2);
                violationRecord.setTappid(mediaId);
                violationRecord.setReduced_amount(estimatedReduction);
                recordList.add(violationRecord);
            }
            Document doc = Jsoup.parse(htmlContent);
            Elements rows = doc.select("table tbody tr");
            // 遍历表格行（跳过第一行表头）
            for (int i = 1; i < rows.size(); i++) {
                Element row = rows.get(i);
                Elements columns = row.select("td");
                if (columns.size() >= 3) {
                    String mediaId = columns.get(0).text().trim();
                    String adSlotId = columns.get(1).text().trim();
                    String amount = columns.get(2).text().trim();
                    OppoViolationRecord record = new OppoViolationRecord();
                    BeanUtils.copyProperties(violationRecord, record);
                    record.setMessage_id(record.getMessage_id() + mediaId);
                    record.setTappid(mediaId);
                    record.setAdsense_id(adSlotId);
                    record.setAmount(amount);
                    recordList.add(record);
                }
            }
            return recordList;
        } else if (subject.contains("质量分额外补贴金额通知")) {
            //媒体符合领航计划的质量分额外补贴政策
            if (violationContent.contains("符合领航计划的质量分额外补贴政策")) {
                emailMainKey = "媒体符合领航计划的质量分额外补贴政策";
            }
            violationRecord.setViolation_content(emailMainKey);

            //质量分额外补贴金额通知 提取邮件关键字
            String regex = "媒体ID[:：](\\d+).*?(\\d+)月质量分：([\\d.]+).*?(\\d+)月质量分额外补贴金额[:：]([\\d.]+)";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(violationContent);
            // 提取数据
            if (matcher.find()) {
                String mediaId = matcher.group(1); // 媒体ID
                String qualityScore = matcher.group(3); // 质量分
                String subsidyAmount = matcher.group(5); // 质量分额外补贴金额
                violationRecord.setTappid(mediaId);
                violationRecord.setScore(qualityScore);
                violationRecord.setAmount(subsidyAmount);
            }
            recordList.add(violationRecord);
            return recordList;
        } else {
            //流量异常 提取邮件关键字
            if (subject.contains("OPPO联盟媒体流量排查通知")) {
                //获取关键字
                String pattern1 = "具体表现为：([^；]+)";
                Pattern r1 = Pattern.compile(pattern1);
                Matcher m1 = r1.matcher(violationContent);
                if (m1.find()) {
                    String behaviors = m1.group(1);
                    emailMainKey += "具体表现为: " + behaviors;
                }
                // 正则表达式提取“5个工作日”
                String pattern2 = "收到本通知(\\d+)个工作日内";
                Pattern r2 = Pattern.compile(pattern2);
                Matcher m2 = r2.matcher(violationContent);
                if (m2.find()) {
                    String days = m2.group(1);
                    emailMainKey += ",收到本通知" + days + "个工作日内提供申述材料";
                }
                violationRecord.setViolation_content(emailMainKey);
                // 获取维度，维度id，违规日期
                String regex3 = "维度\\s+维度ID\\s+违规日期\\s+(.+)\\s+((?:\\d+,?)+)\\s+(\\d{4}年\\d{1,2}月)";
                Pattern pattern3 = Pattern.compile(regex3);
                Matcher matcher3 = pattern3.matcher(violationContent);
                // 提取数据
                if (matcher3.find()) {
                    String dimension = matcher3.group(1); // 维度
                    String dimensionId = matcher3.group(2); // 维度ID
                    String violationDate = matcher3.group(3); // 违规日期
                    //广告位ID：取邮件中维度是广告位的维度ID ,平台appid：取邮件中维度是媒体的维度ID
                    violationRecord.setViolation_date(violationDate);
                    violationRecord.setDimension(dimension);
                    if ("广告位".equals(dimension)) {
                        String[] list = dimensionId.split(",");
                        for (String id : list) {
                            OppoViolationRecord record = new OppoViolationRecord();
                            BeanUtils.copyProperties(violationRecord, record);
                            record.setMessage_id(record.getMessage_id() + id);
                            record.setAdsense_id(id);
                            recordList.add(record);
                        }
                    } else if ("媒体".equals(dimension)) {
                        String[] list = dimensionId.split(",");
                        for (String id : list) {
                            OppoViolationRecord record = new OppoViolationRecord();
                            BeanUtils.copyProperties(violationRecord, record);
                            record.setMessage_id(record.getMessage_id() + id);
                            record.setTappid(id);
                            recordList.add(record);
                        }
                    }
                }
                return recordList;
            } else if (subject.contains("流量排查逾期通知")) {
                emailMainKey = "在规定期限内未按照OPPO联盟的要求辅助提供相应的排查信息";
            } else if (subject.contains("流量异常排查结果通知")) {
                Pattern pattern = Pattern.compile("再次核实，(.*?)。");
                Matcher matcher = pattern.matcher(violationContent);
                if (matcher.find()) {
                    emailMainKey = matcher.group(1).trim(); // 去除首尾空格
                } else {
                    if (violationContent.contains("申诉成功通过")) {
                        emailMainKey = "申诉成功通过";
                    }
                }
            }
            violationRecord.setViolation_content(emailMainKey);
            //维度 维度ID 媒体 31720167
            //维度 维度ID 广告位 1570558
            String regex2 = "维度\\s+维度ID\\s+(.+)\\s+((?:\\d+,?)+)";
            Pattern pattern2 = Pattern.compile(regex2);
            Matcher matcher2 = pattern2.matcher(violationContent);
            if (matcher2.find()) {
                String dimension = matcher2.group(1); // 维度
                String dimensionId = matcher2.group(2); // 维度ID
                violationRecord.setDimension(dimension);
                if ("广告位".equals(dimension)) {
                    String[] list = dimensionId.split(",");
                    for (String id : list) {
                        OppoViolationRecord record = new OppoViolationRecord();
                        BeanUtils.copyProperties(violationRecord, record);
                        record.setMessage_id(record.getMessage_id() + id);
                        record.setAdsense_id(id);
                        recordList.add(record);
                    }
                } else if ("媒体".equals(dimension)) {
                    String[] list = dimensionId.split(",");
                    for (String id : list) {
                        OppoViolationRecord record = new OppoViolationRecord();
                        BeanUtils.copyProperties(violationRecord, record);
                        record.setMessage_id(record.getMessage_id() + id);
                        record.setTappid(id);
                        recordList.add(record);
                    }

                }
            }
            return recordList;
        }
    }

    @Override
    public Result<List<OppoViolationRecordVo>> queryList(OppoViolationRecordDTO dto) {
        List<OppoViolationRecordVo> recordVos;
        long totalSize;
        dto.setAppid(userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), dto.getAppCategory(), dto.getAppid()));
        if (dto.isPageFlag()) {
            //分页查询
            PageHelper.startPage(dto.getStart(), dto.getLimit());
            List<OppoViolationRecordVo> recordList = violationRecordMapper.queryListV2(dto);
            PageInfo<OppoViolationRecordVo> pageInfo = new PageInfo<>(recordList);
            //数据格式转换操作
            recordVos = pageInfo.getList();
            totalSize = pageInfo.getTotal();
        } else {
            //不分页查询
            recordVos = violationRecordMapper.queryListV2(dto);
            totalSize = recordVos.size();
        }
        for (OppoViolationRecordVo recordVo : recordVos) {
            //广告配置相关字段格式转换操作
            //绑定模式
            if (!StringUtils.isEmpty(recordVo.getBidding_mode())) {
                recordVo.setBidding_mode(BIDDING_MODE.get(recordVo.getBidding_mode()));
            }
            //类型
            if (!StringUtils.isEmpty(recordVo.getType())) {
                recordVo.setType(VIOLATION_TYPE.get(recordVo.getType()));
            }
            //sdk广告源类型
            if (!StringUtils.isEmpty(recordVo.getSdk_adtype())) {
                recordVo.setSdk_adtype(SKD_AD_TYPE.get(recordVo.getSdk_adtype()));
            }
            //广告使用类型
            if (!StringUtils.isEmpty(recordVo.getOpen_type())) {
                recordVo.setOpen_type(AD_USAGE_TYPE.get(recordVo.getOpen_type()));
            }
            //ctr封装百分号
            if (!StringUtils.isEmpty(recordVo.getCtr())) {
                recordVo.setCtr(recordVo.getCtr() + "%");
            }
        }
        return ResultUtils.success(Constants.OK, recordVos, null, totalSize);
    }


    /**
     * 去除文本中的表情包，防止插入数据库失败
     *
     * @param content
     * @return
     */
    public static String removeEmoji(String content) {
        if (content == null) {
            return null;
        }
        // 过滤掉 Unicode 超过 U+FFFF（即 4 字节字符）的内容
        return content.replaceAll("[\\x{10000}-\\x{10FFFF}]", "");
    }


}
