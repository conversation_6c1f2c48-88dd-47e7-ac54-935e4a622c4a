package com.wbgame.service.impl;

import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.EmailVerificationCodeMapper;
import com.wbgame.pojo.EmailVerificationCodeDTO;
import com.wbgame.pojo.EmailVerificationCodeVo;
import com.wbgame.pojo.OppoViolationRecord;
import com.wbgame.service.EmailVerificationCodeService;
import com.wbgame.service.OppoViolationRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description 邮件验证码业务层实现
 * @Date 2025/01/16
 */
@Service
public class EmailVerificationCodeServiceImpl implements EmailVerificationCodeService {

    private static final Logger logger = LoggerFactory.getLogger(EmailVerificationCodeServiceImpl.class);

    @Autowired
    private EmailVerificationCodeMapper emailVerificationCodeMapper;

    @Autowired
    private OppoViolationRecordService oppoViolationRecordService;


    // 华为验证码正则：验证码： 173646
    private static final Pattern HUAWEI_PATTERN = Pattern.compile("验证码[：:]\\s*(\\d{6})");

    // 小米验证码正则：验证码是：303729
    private static final Pattern XIAOMI_PATTERN = Pattern.compile("验证码是[：:]\\s*(\\d{6})");


    // 华为账号信息正则：华为账号 171******08
    private static final Pattern HUAWEI_ACCOUNT_PATTERN = Pattern.compile("华为账号\\s+([\\d*]+)");

    // 小米账号信息正则：小米账号15*****212
    private static final Pattern XIAOMI_ACCOUNT_PATTERN = Pattern.compile("小米用户([\\d*]+)");

    // 荣耀账号信息正则：荣耀账号 177******23
    private static final Pattern HONOR_ACCOUNT_PATTERN = Pattern.compile("荣耀账号\\s+([\\d*]+)");

    @Async
    @Override
    public String fetchEmailsAndParseCode(String accountInfo) {
        try {
            logger.info("开始拉取邮件并解析验证码，账户：{}", accountInfo);

            // 调用现有service拉取邮件
            String email = "<EMAIL>";
            List<OppoViolationRecord> emailList = oppoViolationRecordService.fetchEmailsOfPrompts(
                0, email, "账号邮件验证,登录验证".split(","), null);

            if (CollectionUtils.isEmpty(emailList)) {
                logger.info("未拉取到包含验证码的邮件");
                return ReturnJson.toErrorJson("未找到包含验证码的邮件");
            }

            List<EmailVerificationCodeVo> codeList = new ArrayList<>();
            int successCount = 0;

            for (OppoViolationRecord record : emailList) {
                EmailVerificationCodeVo code = parseVerificationCode(record, email);
                if (code != null) {
                    codeList.add(code);
                    successCount++;
                }
            }

            // 批量保存到数据库
            if (!CollectionUtils.isEmpty(codeList)) {
                emailVerificationCodeMapper.batchInsert(codeList);
                logger.info("成功解析并保存{}条验证码记录", successCount);
            }

            String result = String.format("成功处理%d封邮件，解析出%d个验证码", emailList.size(), successCount);
            logger.info(result);
            return ReturnJson.success(result);

        } catch (Exception e) {
            logger.error("拉取邮件并解析验证码异常：", e);
            return ReturnJson.toErrorJson("处理失败：" + e.getMessage());
        }
    }

    @Override
    public EmailVerificationCodeVo queryVerificationCodes(EmailVerificationCodeDTO dto) {
        try {
            logger.info("查询验证码记录，参数：{}", dto);
            EmailVerificationCodeVo resultList = emailVerificationCodeMapper.queryByEmail(dto);

            return resultList;

        } catch (Exception e) {
            logger.error("查询验证码记录异常：", e);
            return null;
        }
    }

    /**
     * 解析邮件中的验证码
     */
    private EmailVerificationCodeVo parseVerificationCode(OppoViolationRecord record, String email) {
        if (StringUtils.isEmpty(record.getContent())) {
            return null;
        }
        
        String content = record.getContent();
        String verificationCode = null;
        String platform = null;
        String accountInfo = null;

        // 尝试匹配华为格式
        Matcher huaweiMatcher = HUAWEI_PATTERN.matcher(content);
        if (huaweiMatcher.find()) {
            verificationCode = huaweiMatcher.group(1);
            platform = "华为";
            if(content.contains("荣耀")){
                platform = "荣耀";
            }
        }
        if(verificationCode == null){
            // 尝试匹配小米格式
            Matcher xiaomiMatcher = XIAOMI_PATTERN.matcher(content);
            if (xiaomiMatcher.find()) {
                verificationCode = xiaomiMatcher.group(1);
                platform = "小米";
            }
        }
        /* 增加华为账号信息的正则获取 */
        if(accountInfo == null){
            // 尝试匹配华为账号信息
            Matcher huaweiAccountMatcher = HUAWEI_ACCOUNT_PATTERN.matcher(content);
            if (huaweiAccountMatcher.find()) {
                accountInfo = huaweiAccountMatcher.group(1);
            }
        }
        if(accountInfo == null){
            // 尝试匹配小米账号信息
            Matcher xiaomiAccountMatcher = XIAOMI_ACCOUNT_PATTERN.matcher(content);
            if (xiaomiAccountMatcher.find()) {
                accountInfo = xiaomiAccountMatcher.group(1);
            }
        }
        if(accountInfo == null){
            // 尝试匹配荣耀账号信息
            Matcher honorAccountMatcher = HONOR_ACCOUNT_PATTERN.matcher(content);
            if (honorAccountMatcher.find()) {
                accountInfo = honorAccountMatcher.group(1);
            }
        }

        if (verificationCode == null) {
            logger.warn("未能解析出验证码，邮件ID：{}", record.getMessage_id());
            return null;
        }
        
        EmailVerificationCodeVo code = new EmailVerificationCodeVo();
        code.setEmail(email);
        code.setMessageId(record.getMessage_id());
        code.setSubject(record.getSubject());
        code.setInternalDate(record.getViolation_date());
        code.setContent(content);
        code.setVerificationCode(verificationCode);
        code.setPlatform(platform);
        code.setAccountInfo(accountInfo);
        code.setCreateTime(new Date());
        code.setUpdateTime(new Date());
        
        logger.info("成功解析验证码：{}，平台：{}，账号：{}", verificationCode, platform, accountInfo);
        return code;
    }
}
