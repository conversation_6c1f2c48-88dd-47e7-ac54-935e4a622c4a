package com.wbgame.service.adv2.adcode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.adv2.AdvCommonAdCodeMapper;
import com.wbgame.mapper.master.AdCodeConfigMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.PlatformDataMapper;
import com.wbgame.pojo.adv2.*;
import com.wbgame.service.adv2.AdCodeConfigService;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;


/**
 * <AUTHOR>
 * @date 2024/11/08
 * @description xiaomi创建广告位业务层
 **/
@Service
@Slf4j
public class XiaomiAdCodeService {

    public static final String PLATFORM_XIAOMI_NAME = "Mi";

    //小米广告位创建url
    private static final String CREATE_URL = "https://dev.mi.com/memberapi/placement/create";

    //小米广告位查询url
    private static final String LIST_URL = "https://dev.mi.com/memberapi/placement/list";

    @Resource
    private AdCodeConfigService adCodeConfigServiceImpl;
    @Resource
    private AdCodeConfigMapper adCodeConfigMapper;
    @Resource
    private AdvCommonAdCodeMapper advCommonAdCodeMapper;
    @Resource
    private Adv2Mapper adv2Mapper;
    @Autowired
    private AdMapper adMapper;
    @Autowired
    private PlatformDataMapper platformDataMapper;

    @Resource
    private Adv2Service adv2Service;

    //初始化一些数据至map中存储
    public static final Map<String, String> SDK_AD_TYPE_MAP = new HashMap<String, String>() {{
        put("开屏", "splash");
        put("原生开屏", "natSplash");
        put("普通banner/模板", "banner");
        put("banner自渲染", "natBanner");
        put("信息流模板", "yuans");
        put("信息流自渲染", "msg");
        put("普通插屏/模板", "plaque");
        put("自渲染插屏", "natPlaque");
        put("插屏视频", "plaqueVideo");
        put("视频", "video");
        put("视频自渲染", "natVideo");
        put("icon", "icon");
    }};

    public static final Set<String> OPEN_TYPE_SET = Sets.newHashSet("video", "splash", "msg", "banner", "plaque");
    //xiaomi 广告场景
    public static final Map<String, Integer> ADVERTISING_SCENE_MAP = new HashMap<String, Integer>() {{
        //小米--广告场景
        put("无", 0);
        put("其他", 1);
        put("通用", 2);
        put("进入APP时", 3);
        put("退出APP时", 4);
        put("场景/页面切换时", 5);
        put("游戏关卡", 6);
        put("页面顶部", 7);
        put("页面底部", 8);
        put("领取", 1000);
        put("兑换", 1001);
        put("解锁", 1002);
        put("阅读", 1003);
        put("提现", 1004);
        put("解锁新内容", 1005);
        //小米--广告场景
    }};

    public static final Map<String, String> ADTYPE_MAP = new HashMap<String, String>() {{
        put("横幅", "banner");
        put("开屏", "splash");
        put("原生", "msg");
        put("激励视频", "video");
        put("插屏", "plaque");
    }};

    //小米广告位具体类型
    public static final Map<String, Set<String>> AD_TYPE_DETAILS_MAP = new HashMap<String, Set<String>>() {{
        put("开屏", Sets.newHashSet("系统开屏", "应用内横板开屏"));
        put("横幅", Sets.newHashSet("宽版1080*314", "窄版1080*180"));
        put("插屏", Sets.newHashSet("半屏插屏", "全屏视频插屏"));
        put("激励视频", Sets.newHashSet("激励视频-竖版", "激励视频-横版"));
        put("原生", Sets.newHashSet("左文右图(带标题)", "左图右文(带标题A版)", "左图右文(带标题B版)", "上图下文(大图)", "上文下图(组图)", "icon样式(悬浮球)", "icon样式A版", "icon样式B版"));
    }};

    /**
     * 创建小米广告位
     *
     * @param dto 请求参数
     * @return 创建结果
     */
    public Result<String> createAdCode(XiaomiAdDTO dto) {
        StringBuilder errorMsg = new StringBuilder();
        //平台产品appid空值校验
        if (dto.getApp_id() == null && BlankUtils.isBlank(dto.getChannel())) {
            return ResultUtils.failure("创建广告位失败,原因:平台产品id参数未传");
        }

        //根据类型和具体类型，广告素材类型等信息获取创建广告位api所需的style
        Result<Map<String, String>> apiParams = getApiStyle(dto.getAdType(), dto.getDetailType(), dto.getDrawingType(), dto.getReturnElements());
        //获取style存在异常情况
        if (Constants.OK.getRet() != apiParams.getRet()) {
            return ResultUtils.failure(apiParams.getMsg());
        } else {
            //封装style
            Map<String, String> paramMap = apiParams.getData();
            dto.setStyle(paramMap.get("style"));
            dto.setReturnElements(paramMap.get("returnElements"));
            dto.setDrawingType(paramMap.get("drawingType"));
        }
        //sdk广告类型
        String sdkAdType = dto.getSdk_ad_type();
        String appid = dto.getAppid();
        // 广告拓展名:
        String adExtentionName = dto.getAdExtensionName();

        //先判断当前广告位id是否已经存在
        String adsid = PLATFORM_XIAOMI_NAME + "_" + sdkAdType + "_" + appid + "_" + adExtentionName;
        // 广告源中查询
        ExtendAdsidVo adsidVo = adCodeConfigServiceImpl.getDnAdSid(adsid);
        if (!ObjectUtils.isEmpty(adsidVo)) {
            //广告源中存在数据，为重复数据
            return ResultUtils.failure("创建广告位失败,原因:生成规则:广告平台_广告位类型_产品id_扩展名生成的ad-sid 已经在广告源表存在");
        }
        // 获取当前应用的所需账号信息
        PlatformAppInfoVo appInfoParam = new PlatformAppInfoVo();
        appInfoParam.setPlatform("xiaomi");
        appInfoParam.setTappid(dto.getApp_id());
        appInfoParam.setAppid(appid);
        appInfoParam.setChannel(DataTransUtils.transToSql(dto.getChannel()));
        //过滤包名为空的的所有xiaomi平台appid
        Optional<PlatformAppInfoVo> accountOptional = platformDataMapper.getPlatformAppInfoList(appInfoParam).stream().filter(t -> !BlankUtils.checkBlank(t.getPackagename())).findFirst();
        if (!accountOptional.isPresent()) {
            return ResultUtils.failure("创建广告位失败,原因:该产品未配置变现平台账号");
        }
        PlatformAppInfoVo appInfoVo = accountOptional.get();
        String sql = "SELECT account,ttappid,ttparam,cname FROM yyhz_0308.app_channel_config WHERE channel = 'xiaomi' and status = 1 and account ='" + appInfoVo.getTaccount() + "' ";
        //查询xiaomi平台的token信息
        List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);
        if (CollectionUtils.isEmpty(tokenList)) {
            return ResultUtils.failure("创建广告位失败,原因:该产品未配置变现平台账号");
        }
        //封装tappid
        dto.setApp_id(appInfoVo.getTappid());


        Map<String, Object> accountInfo = tokenList.get(0);
        //先创建广告位
        JSONObject adCodeRet = createAdCodeApi(dto, accountInfo);
        // 返回结果
        if (adCodeRet == null) {
            return ResultUtils.failure("创建广告位失败,api返回空");
        }
        //code != 0 为失败结果
        if (adCodeRet.getIntValue("code") != 0) {
            // api错误原因
            return ResultUtils.failure("创建广告位失败,api返回：" + adCodeRet.getString("message"));
        }
        // 创建成功，获取广告位id
        JSONObject adInfoObj = adCodeRet.getJSONObject("adInfo");
        if (ObjectUtils.isEmpty(adInfoObj) || BlankUtils.checkBlank(adInfoObj.getString("placementId"))) {
            return ResultUtils.failure("广告位创建成功,但没有找到创建的广告位id");
        }
        // 创建广告位成功
        errorMsg.append("广告位创建成功");
        //构建小米广告位数据 写入adcode表
        AdvAdcodeXiaomiInfo adcodeXiaomiInfo = buildAdcodeXiaomiInfo(dto, adInfoObj);

        // 写入adcode表、写入广告源表、写入广告配置表
        int adCodeNum = advCommonAdCodeMapper.insertXiaomiAdCode(adcodeXiaomiInfo);
        if (adCodeNum < 1) {
            errorMsg.append(",").append("写入adcode表失败");
        }
        if (!saveXiaomiAdCodeToAdSidManage(adcodeXiaomiInfo)) {
            errorMsg.append(",").append("写入广告源表失败");
        }
        //策略不为空才添加数据至广告配置表中
        if (!StringUtils.isEmpty(adcodeXiaomiInfo.getStrategy())) {
            if (!saveXiaomiAdcodeToDnExtendAdconfig(adcodeXiaomiInfo)) {
                errorMsg.append(",").append("写入广告配置表失败");
            }
        }
        return ResultUtils.success(errorMsg.toString());
    }

    /**
     * 构建小米广告位pojo
     *
     * @param dto       小米广告位数据dto
     * @param adInfoObj 创建广告位后查询信息的data
     * @return 构建结果
     */
    private AdvAdcodeXiaomiInfo buildAdcodeXiaomiInfo(XiaomiAdDTO dto, JSONObject adInfoObj) {
        AdvAdcodeXiaomiInfo adcodeXiaomiInfo = new AdvAdcodeXiaomiInfo();
        BeanUtils.copyProperties(dto, adcodeXiaomiInfo);
        adcodeXiaomiInfo.setTappid(dto.getApp_id());
        //adcodeXiaomiInfo.setCreate_user(dto.getCreate_user());
        //adcodeXiaomiInfo.setAppid(dto.getAppid());
        //adcodeXiaomiInfo.setTappid(dto.getTappid());
        //adcodeXiaomiInfo.setCreateUser(dto.getCreateUser());
        //adcodeXiaomiInfo.setChannel(dto.getChannel());
        //adcodeXiaomiInfo.setSdk_ad_type(dto.getSdk_ad_type());
        //adcodeXiaomiInfo.setOpen_type(dto.getOpen_type());
        //adcodeXiaomiInfo.setAdExtensionName(dto.getAdExtensionName());
        //adcodeXiaomiInfo.setRemark(dto.getRemark());
        //adcodeXiaomiInfo.setStrategy(dto.getStrategy());
        //adcodeXiaomiInfo.setParams(dto.getParams());
        //adcodeXiaomiInfo.setAdName(dto.getAdName());
        //平台查询返回信息封装
        //adcodeXiaomiInfo.setAdType(adInfoObj.getString("styleName"));
        adcodeXiaomiInfo.setPlacementid(adInfoObj.getString("placementId"));
        adcodeXiaomiInfo.setTarget_price(adInfoObj.getString("targetPrice"));
        //adcodeXiaomiInfo.setDetail_style(adInfoObj.getString("detailStyleName"));
        //广告位类型
        adcodeXiaomiInfo.setAdType(dto.getAdType());
        //具体类型
        adcodeXiaomiInfo.setDetail_style(dto.getDetailType());
        //constraintId
        adcodeXiaomiInfo.setConstraintId(dto.getConstraintId());
        //drawing_type
        adcodeXiaomiInfo.setDrawing_type(dto.getDrawingType());
        //return_elements
        adcodeXiaomiInfo.setReturn_elements(dto.getReturnElements());
        //render_type
        adcodeXiaomiInfo.setRender_type(dto.getRenderType());
        //场景-scenes
        adcodeXiaomiInfo.setScenes("0");
        //预估收益
        adcodeXiaomiInfo.setEcpm(dto.getEcpm());
        //占比
        adcodeXiaomiInfo.setConfigRate(dto.getConfigRate());
        return adcodeXiaomiInfo;
    }

    /**
     * 分页查询小米广告位数据
     *
     * @param dto   查询条件
     * @param start 起始页
     * @param limit 每页大小
     * @return 查询结果
     */
    public Result<List<AdvAdcodeXiaomiInfo>> queryXiaomiAdCode(AdvAdcodeXiaomiInfo dto, int start, int limit) {
        //分页查询
        PageHelper.startPage(start, limit);
        List<AdvAdcodeXiaomiInfo> xiaomiAdVos = advCommonAdCodeMapper.selectXiaomiAdCodeList(dto);
        PageInfo<AdvAdcodeXiaomiInfo> respPage = new PageInfo<>(xiaomiAdVos);
        List<AdvAdcodeXiaomiInfo> data = respPage.getList();
        for (AdvAdcodeXiaomiInfo xiaomiAdInfo : data) {
            //屏蔽规则转换
            String constraintId = xiaomiAdInfo.getConstraintId();
            constraintId = StringUtils.isEmpty(constraintId) || "0".equals(constraintId) ? "无屏蔽规则" : "426".equals(constraintId) ? "外部dsp" : "未知";
            xiaomiAdInfo.setConstraintId(constraintId);
            // 屏幕方向: 0-无该选项时,1-原生,3-插屏-半屏插屏,4-插屏-全屏视频插屏
            String drawingType = xiaomiAdInfo.getDrawing_type();
            drawingType = StringUtils.isEmpty(drawingType) || "0".equals(drawingType) || "1".equals(drawingType) ? "无" : "3".equals(drawingType) ? "竖版" : "横版";
            xiaomiAdInfo.setDrawing_type(drawingType);
            //广告素材类型
            String returnElements = xiaomiAdInfo.getReturn_elements();
            returnElements = StringUtils.isEmpty(returnElements) ? "" : "1".equals(returnElements) ? "图片" : "6".equals(returnElements) ? "视频" : "图片,视频";
            xiaomiAdInfo.setReturn_elements(returnElements);
        }
        //返回结果
        return ResultUtils.success(Constants.OK, data, null, respPage.getTotal());
    }

    /**
     * 批量导入
     *
     * @param file 导入文件excel
     * @return 导入结果
     */
    public Result<String> batchImport(MultipartFile file,Boolean isSyncAdpos) throws IOException {

        //文件格式校验
        if (ObjectUtils.isEmpty(file) || !(file.getOriginalFilename().endsWith(".xls") || file.getOriginalFilename().endsWith(".xlsx"))) {
            return ResultUtils.failure("上传文件有误，仅支持excel文件!");
        }
        //读取excel文件
        List<ArrayList<String>> excelList = ExcelUtils.readExcel(file);
        //检测导入的数据格式并转换成创建广告位类型
        Result<List<XiaomiAdDTO>> transResult = transExcelDataFormat(excelList);
        if (transResult.getRet() == null || Constants.OK.getRet() != transResult.getRet()) {
            //数据格式检测异常，直接返回异常结果
            return ResultUtils.failure(transResult.getMsg());
        }
        //获取需要生成广告位的数据
        List<XiaomiAdDTO> adDtoList = transResult.getData();
        log.info("xiaomi batchImport:" + JSON.toJSONString(adDtoList));

        Map<String, String> retMap = new LinkedHashMap<>();
        Map<String,XiaomiAdDTO> adManageDtoMap = new HashMap<>();
        for (int i = 0; i < adDtoList.size(); i++) {
            XiaomiAdDTO adDTO = adDtoList.get(i);
            Result<String> createResult = createAdCode(adDtoList.get(i));
            if (Constants.OK.getRet() != createResult.getRet()) {
                retMap.put(Integer.toString(i + 1), createResult.getMsg());
            }
            //看是否需要将数据同步至广告位管理
            adManageDtoMap.put(adDTO.getAppid()+adDTO.getChannel(),adDTO);
        }
        StringBuilder syncAdManageResult = new StringBuilder();
        if (MapUtils.isNotEmpty(adManageDtoMap) && isSyncAdpos != null && isSyncAdpos) {
            syncAdManageResult.append("同步到广告位管理结果：");
            for (Map.Entry<String, XiaomiAdDTO> entry : adManageDtoMap.entrySet()) {
                XiaomiAdDTO adDTO = entry.getValue();
                ExtendAdsidVo extendAdsidVo = new ExtendAdsidVo();
                extendAdsidVo.setCha_id(adDTO.getChannel());
                extendAdsidVo.setAppid(adDTO.getAppid());
                String syncRet = adv2Service.syncAdsidToAdpos(Lists.newArrayList(extendAdsidVo), adDTO.getCreateUser());
                syncAdManageResult.append(syncRet).append("<br>");
            }
        }
        StringBuilder sb = new StringBuilder();
        if (retMap.size() > 0) {
            for (Map.Entry<String, String> ret : retMap.entrySet()) {
                sb.append(ret.getKey()).append(ret.getValue()).append("</br>");
            }
            return ResultUtils.failure(syncAdManageResult + sb.append(retMap.size() < adDtoList.size() ? ",其余"+(adDtoList.size()-retMap.size())+"条数据创建成功" : "").toString());
        } else {
            return ResultUtils.success(syncAdManageResult+"广告位全部创建成功");
        }
    }

    /**
     * 导入数据解析校验转换操作成创建广告位dto
     *
     * @param excelList 导入的excel数据
     * @return Result<List < XiaomiAdDTO>> 转换结果：ret= 0 表示失败，ret = 1 表示校验成功，转换数据保存再data中
     */
    private Result<List<XiaomiAdDTO>> transExcelDataFormat(List<ArrayList<String>> excelList) {
        //导入数据判空
        if (CollectionUtils.isEmpty(excelList) || excelList.size() == 1) {
            return ResultUtils.failure("导入数据为空！");
        }
        //当前操作人
        String username = LOGIN_USER_NAME.get();
        StringBuilder failMsg = new StringBuilder();
        List<XiaomiAdDTO> adInfoDtoList = new ArrayList<>();
        for (int i = 1; i < excelList.size(); i++) {
            //第一行是标题行
            ArrayList<String> rowsExcel = excelList.get(i);
            //过滤行数据为空的数据
            boolean blankMatch = rowsExcel.stream().allMatch(BlankUtils::checkBlank);
            if (blankMatch) continue;
            StringBuilder message = new StringBuilder();
            //数据封装dto
            XiaomiAdDTO adDto = new XiaomiAdDTO();
            //数据校验
            //1： sdk广告源样式 // 需要格式转换操作
            String sdkAdType = rowsExcel.get(0);
            if (!SDK_AD_TYPE_MAP.containsKey(sdkAdType)) {
                message.append("sdk广告源样式必填或sdk广告源样式错误;");
            } else {
                adDto.setSdk_ad_type(SDK_AD_TYPE_MAP.get(sdkAdType));
            }
            //2--广告使用类型
            if (!OPEN_TYPE_SET.contains(rowsExcel.get(1))) {
                message.append("广告使用类型取值错误;");
            } else {
                adDto.setOpen_type(rowsExcel.get(1));
            }
            //3--广告位类型
            String adType = rowsExcel.get(2);
            //4--具体类型
            String detailType = rowsExcel.get(3);
            //5-策略
            //6-区分应用内外(0-应用内，1-应用外)
            String out = rowsExcel.get(5);
            if (BlankUtils.checkBlank(out) || (!"0".equals(out) && !"1".equals(out))) {
                message.append("区分应用内外需必填且需为0或1;");
            } else {
                adDto.setOut(out);
            }
            //7-bidding模式(0-否，1-c2sbidding,2-s2sbidding)
            String bidding = rowsExcel.get(6);
            if (BlankUtils.checkBlank(bidding) || (!"0".equals(bidding) && !"1".equals(bidding) && !"2".equals(bidding))) {
                message.append("bidding模式需必填且需为0或1或2;");
            } else {
                adDto.setBidding(bidding);
            }
            //8-广告拓展名
            if (BlankUtils.checkBlank(rowsExcel.get(7))) {
                message.append("广告扩展名需必填;");
            } else {
                adDto.setAdExtensionName(rowsExcel.get(7));
            }
            //9-广告位名称
            if (BlankUtils.checkBlank(rowsExcel.get(8))) {
                message.append("广告位名称需必填;");
            } else {
                adDto.setAdName(rowsExcel.get(8));
            }
            //10-平台appid
            if (BlankUtils.checkBlank(rowsExcel.get(9))) {
                message.append("平台appid需必填;");
            } else {
                adDto.setApp_id(rowsExcel.get(9));
            }
            //11-产品id
            if (BlankUtils.checkBlank(rowsExcel.get(10))) {
                message.append("产品id需必填;");
            } else {
                adDto.setAppid(rowsExcel.get(10));
            }
            //12-子渠道
            if (BlankUtils.checkBlank(rowsExcel.get(11))) {
                message.append("子渠道需必填;");
            } else {
                adDto.setChannel(rowsExcel.get(11));
            }
            //13-屏幕方向
            String drawingType = rowsExcel.size() >= 13 ? rowsExcel.get(12) : "";

            //14-广告素材类型(1-图片,6-视频)
            //广告素材类型-:1,6
            String returnElements = rowsExcel.size() >= 14 ? rowsExcel.get(13) : "";

            //15-屏蔽规则(0-无屏蔽规则,426-外部dsp)
            //屏蔽规则-必填
            // 正则表达式匹配 "0", "426"
            String constraintIdPattern = "^(0|426)$";
            String constraintId = rowsExcel.size() >= 15 ? rowsExcel.get(14) : "";
            if (!BlankUtils.checkBlank(constraintId) && !constraintId.matches(constraintIdPattern)) {
                message.append("屏蔽规则格式需仅为0或426;");
            } else {
                adDto.setConstraintId(BlankUtils.checkBlank(constraintId) ? "0" : constraintId);
            }
            //16-预估ecpm(填充到配置表中)

            //广告位类型校验
            if (!AD_TYPE_DETAILS_MAP.containsKey(adType.trim())) {
                message.append("广告位类型需必填或格式异常");
            } else {
                //类型封装
                adDto.setAdType(adType.trim());
                //具体类型
                Set<String> detailSet = AD_TYPE_DETAILS_MAP.get(adDto.getAdType());
                if (BlankUtils.checkBlank(detailType) || !detailSet.contains(detailType.trim())) {
                    message.append("具体类型需必填或格式异常;");
                }
                //屏幕方向格式校验
                if ("插屏".equals(adDto.getAdType())) {
                    //校验屏幕方向
                    if (BlankUtils.checkBlank(drawingType) || (!"3".equals(drawingType) && !"4".equals(drawingType))) {
                        message.append("类型为插屏时屏幕方向必填且需为3或4");
                    }
                }
            }
            //api所需的style，drawingType 参数获取
            //存在异常，当前条数据校验失败
            if (message.length() != 0) {
                message.insert(0, "第" + i + "条：");
                failMsg.append(message);
                continue;
            }
            //广告素材类型格式校验
            Result<String> checked = checkReturnElements(returnElements, adDto.getAdType(), detailType.trim());
            if (Constants.OK.getRet() != checked.getRet()) {
                message.append(checked.getMsg());
                failMsg.append(message);
                continue;
            }
            //屏幕方向
            adDto.setDrawingType(drawingType);
            //广告素材类型
            adDto.setReturnElements(returnElements);
            //具体类型
            adDto.setDetailType(detailType.trim());
            //当前操作人
            adDto.setCreateUser(username);
            // 5-策略
            adDto.setStrategy(rowsExcel.get(4));

            // 16-预估ecpm
            if (rowsExcel.size() >= 16) {
                adDto.setEcpm(rowsExcel.get(15));
            }
            // 17-分配比例
            if (rowsExcel.size() >= 17) {
                adDto.setConfigRate(rowsExcel.get(16));
            }
            // 18-自定义参数
            if (rowsExcel.size() >= 18) {
                adDto.setParams(rowsExcel.get(17));
            }
            // 19-备注
            if (rowsExcel.size() >= 19) {
                adDto.setRemark(rowsExcel.get(18));
            }
            adInfoDtoList.add(adDto);
        }
        if (failMsg.length() != 0) {
            return ResultUtils.failure(failMsg.toString());
        }
        //校验成功
        return ResultUtils.success(adInfoDtoList);
    }

    /**
     * 广告素材类型格式校验
     *
     * @param returnElements 广告素材类型
     * @param adType         广告类型
     * @param detailType     广告具体类型
     * @return 校验结果
     */
    private Result<String> checkReturnElements(String returnElements, String adType, String detailType) {
        //只有类型为插屏或原生才有对应的广告素材类型
        if ("插屏".equals(adType) || "原生".equals(adType)) {
            String elementsPattern = "^(1|6|1,6|6,1)$";
            if ("半屏插屏".equals(detailType)) {
                if (BlankUtils.checkBlank(returnElements) || !returnElements.matches(elementsPattern)) {
                    return ResultUtils.failure("具体类型为半屏插屏时广告素材类型格式需为1或6或1,6");
                }
            } else if ("左文右图(带标题)".equals(detailType)) {
                if (BlankUtils.checkBlank(returnElements) || !returnElements.matches(elementsPattern)) {
                    return ResultUtils.failure("具体类型为左文右图(带标题)时广告素材类型格式需为1或6或1,6");
                }
            } else if ("左图右文(带标题A版)".equals(detailType)) {
                if (BlankUtils.checkBlank(returnElements) || !returnElements.matches(elementsPattern)) {
                    return ResultUtils.failure("具体类型为左图右文(带标题A版)时广告素材类型格式需为1或6或1,6");
                }
            } else if ("左图右文(带标题B版)".equals(detailType)) {
                if (BlankUtils.checkBlank(returnElements) || !returnElements.matches(elementsPattern)) {
                    return ResultUtils.failure("具体类型为左图右文(带标题B版)时广告素材类型格式需为1或6或1,6");
                }
            } else if ("上图下文(大图)".equals(detailType)) {
                if (BlankUtils.checkBlank(returnElements) || !returnElements.matches(elementsPattern)) {
                    return ResultUtils.failure("具体类型为上图下文(大图)时广告素材类型格式需为1或6或1,6");
                }
            }
        }
        return ResultUtils.success();
    }

    /**
     * 根据类型和具体类型，广告素材类型等信息获取创建广告位所需的api参数style
     *
     * @param adType         广告类型
     * @param detailType     具体类型
     * @param drawingType    屏幕方向
     * @param returnElements 广告素材类型
     * @return 获取结果
     */
    private Result<Map<String, String>> getApiStyle(String adType, String detailType, String drawingType, String returnElements) {

        if (BlankUtils.checkBlank(adType) || !AD_TYPE_DETAILS_MAP.containsKey(adType.trim())) {
            return ResultUtils.failure("广告位类型需必填或格式异常");
        }
        //去除空格
        adType = adType.trim();
        Set<String> detailSet = AD_TYPE_DETAILS_MAP.get(adType);
        if (BlankUtils.checkBlank(detailType) || !detailSet.contains(detailType.trim())) {
            return ResultUtils.failure("具体类型需必填或格式异常;");
        }
        //校验广告素材类型格式是否正确
        Result<String> checked = checkReturnElements(returnElements, adType, detailType);
        if (Constants.OK.getRet() != checked.getRet()) {
            return ResultUtils.failure(checked.getMsg());
        }
        //去除空格
        detailType = detailType.trim();
        String style = null;
        //根据具体的类型获取对应的style
        if ("开屏".equals(adType) && "系统开屏".equals(detailType)) {
            //开屏
            style = "41";
            drawingType = "0";
            returnElements = "";
        } else if ("开屏".equals(adType) && "应用内横板开屏".equals(detailType)) {
            //开屏
            style = "42";
            drawingType = "0";
            returnElements = "";
        } else if ("横幅".equals(adType) && "宽版1080*314".equals(detailType)) {
            //横幅
            style = "50";
            drawingType = "0";
            returnElements = "";
        } else if ("横幅".equals(adType) && "窄版1080*180".equals(detailType)) {
            //横幅
            style = "52";
            drawingType = "0";
            returnElements = "";
        } else if ("插屏".equals(adType) && "半屏插屏".equals(detailType)) {
            //插屏
            //校验屏幕方向
            if (BlankUtils.checkBlank(drawingType) || (!"3".equals(drawingType) && !"4".equals(drawingType))) {
                return ResultUtils.failure("具体类型为半屏插屏时屏幕方向必填且需为3或4");
            }
            if ("3".equals(drawingType)) {
                //屏幕方向为竖版
                style = "1".equals(returnElements) ? "20" : "6".equals(returnElements) ? "24" : "26";
            } else {
                //屏幕方向为横版
                style = "1".equals(returnElements) ? "21" : "6".equals(returnElements) ? "25" : "27";
            }
        } else if ("插屏".equals(adType) && "全屏视频插屏".equals(detailType)) {
            //插屏
            //校验屏幕方向
            if (BlankUtils.checkBlank(drawingType) || (!"3".equals(drawingType) && !"4".equals(drawingType))) {
                return ResultUtils.failure("具体类型为全屏视频插屏时屏幕方向必填且需为3或4");
            }
            if ("3".equals(drawingType)) {
                //屏幕方向为竖版
                style = "22";
                returnElements = "";
            } else {
                //屏幕方向为横版
                style = "23";
                returnElements = "";
            }
        } else if ("激励视频".equals(adType) && "激励视频-竖版".equals(detailType)) {
            //激励视频-竖版
            style = "82";
            drawingType = "0";
            returnElements = "";
        } else if ("激励视频".equals(adType) && "激励视频-横版".equals(detailType)) {
            //激励视频-横版
            style = "81";
            drawingType = "0";
            returnElements = "";
        } else if ("原生".equals(adType)) {
            drawingType = "1";
            if ("左文右图(带标题)".equals(detailType)) {
                style = "112";
            } else if ("左图右文(带标题A版)".equals(detailType)) {
                style = "113";
            } else if ("左图右文(带标题B版)".equals(detailType)) {
                style = "114";
            } else if ("上图下文(大图)".equals(detailType)) {
                style = "115";
            } else if ("上文下图(组图)".equals(detailType)) {
                style = "116";
                returnElements = "";
            } else if ("icon样式(悬浮球)".equals(detailType)) {
                style = "119";
                returnElements = "";
            } else if ("icon样式A版".equals(detailType)) {
                style = "111";
                returnElements = "";
            } else if ("icon样式B版".equals(detailType)) {
                style = "118";
                returnElements = "";
            }
        }
        //结果封装
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("style", style);
        resultMap.put("drawingType", drawingType);
        resultMap.put("returnElements", returnElements);

        return ResultUtils.success(resultMap);
    }

    /**
     * 创建小米广告位
     *
     * @param dto         创建广告位相关信息
     * @param accountInfo 账号信息
     * @return 创建结果
     */
    private JSONObject createAdCodeApi(XiaomiAdDTO dto, Map<String, Object> accountInfo) {
        String cookie = Optional.ofNullable(accountInfo.get("ttappid")).map(Object::toString).orElse(Strings.EMPTY);
        String userId = Optional.ofNullable(accountInfo.get("ttparam")).map(Object::toString).orElse(Strings.EMPTY);
        //判空
        if (StringUtils.isEmpty(cookie) || StringUtils.isEmpty(userId)) {
            return null;
        }
        //请求头数据封装
        Map<String, String> headMap = new HashMap<>();
        //处理cookie中带中文字符
        cookie = removeCookieWithInvilad(cookie);
        headMap.put("cookie", cookie);
        headMap.put("content-type", "application/json");
        //请求参数封装
        Map<String, Object> params = new HashMap<>();
        //广告位名称(必填)
        params.put("name", dto.getAdName());
        //所属应用（必填）
        params.put("publisherId", dto.getApp_id());
        //广告位类型(必填)
        params.put("style", dto.getStyle());
        params.put("subStyle", 0);
        //"0-无屏蔽规则
        //426-外部dsp"
        params.put("constraintId", dto.getConstraintId());
        params.put("drawingType", dto.getDrawingType());
        params.put("returnElements", dto.getReturnElements());
        //封装固定值
        params.put("targetPrice", -1);
        params.put("skipButtonTime", -1);
        params.put("scenes", 0);
        params.put("activities", "");
        params.put("styleList", new JSONArray());
        params.put("originTemplate", null);
        JSONObject deliverRule = new JSONObject();
        deliverRule.put("limitTimeRegion", new JSONArray());
        deliverRule.put("limitCity", new JSONArray());
        params.put("deliverRule", deliverRule);
        log.info("小米创建广告位 api参数：{}, header: {}", JSONObject.toJSONString(params), JSONObject.toJSONString(headMap));
        String responseMsg = null;
        try {
            responseMsg = HttpRequest.httpPostJson(CREATE_URL + "?userId=" + userId, params, headMap);
        } catch (Exception e) {
            log.error("小米创建广告位失败，失败原因：" + e);
            FeishuUtils.sendFeiShu("小米创建广告位失败,失败原因：" + e, "xiaoxh");
            return null;
        }
        log.info("小米创建广告位 api返回信息: {}", responseMsg);
        //{"code":0,"message":"succeed"}
        if (BlankUtils.checkBlank(responseMsg)) {
            return null;
        }
        JSONObject responseObj = JSONObject.parseObject(responseMsg);
        //{"code":0,"message":"succeed"}
        if (responseObj.getIntValue("code") == 0) {
            //查询创建的广告位id
            try {
                //当前日起始时间
                long millis = DateTime.now().withTimeAtStartOfDay().getMillis();
                String placementParam = URLEncoder.encode(dto.getAdName(), "UTF-8");
                String queryListUrl = LIST_URL + "?userId=" + userId + "&publisherParam=" + dto.getApp_id() + "&placementParam=" + placementParam + "&updateStartTime=" + millis + "&updateEndTime=" + millis + "&pageNumber=1&pageSize=20";
                String queryResp = HttpRequest.get(queryListUrl, headMap);
                JSONObject queryRespObj = JSONObject.parseObject(queryResp);
                JSONObject adInfo = getXiaomiAdInfo(queryRespObj);
                responseObj.put("adInfo", adInfo);
            } catch (Exception e) {
                log.error("小米广告位查询异常，异常原因：" + e);
                FeishuUtils.sendFeiShu("小米广告位查询异常，异常原因：" + e, "xiaoxh");
            }
        }
        return responseObj;
    }


    /**
     * 根据查询结果获取最新的一条符合条件的广告位id
     *
     * @param queryRespObj 查询结果
     * @return 广告位id
     */
    private JSONObject getXiaomiAdInfo(JSONObject queryRespObj) {
        if (ObjectUtils.isEmpty(queryRespObj)) {
            return null;
        }
        int code = queryRespObj.getIntValue("code");
        if (code != 0) {
            log.error("小米查询广告位返回异常，原因：" + queryRespObj.getString("message"));
            return null;
        }
        JSONObject data = queryRespObj.getJSONObject("data");
        if (ObjectUtils.isEmpty(data) || !data.containsKey("list")) {
            log.error("小米查询广告位数据为空");
            return null;
        }
        JSONArray list = data.getJSONArray("list");
        //获取创建时间最新的一条数据
        long latestCreateTime = 0;
        //String latestPlacementId = null;
        JSONObject placementObj = null;
        for (int i = 0; i < list.size(); i++) {
            JSONObject placement = list.getJSONObject(i);
            long createTime = placement.getLong("createTime");
            // 更新最新创建时间的placementId
            if (createTime > latestCreateTime) {
                placementObj = placement;
                latestCreateTime = createTime;
                //latestPlacementId = placement.getString("placementId");
            }
        }
        return placementObj;
    }


    /**
     * 移除cookie中中文干扰
     *
     * @param cookie
     * @return
     */
    public static String removeCookieWithInvilad(String cookie) {
        if (!BlankUtils.checkBlank(cookie)) {
            if (cookie.contains("devname")) {
                String[] cookieStrs = cookie.split(";");
                StringBuffer stringBuffer = new StringBuffer();
                for (String each : cookieStrs) {
                    if (!each.contains("devname")) {
                        stringBuffer.append(each).append(";");
                    }
                }
                cookie = stringBuffer.toString();
            }
        }
        return cookie;
    }


    /**
     * 保存小米创建的广告位至广告源管理表 dn_extend_adsid_manage
     *
     * @param adCodeInfo 小米广告位数据
     * @return 创建结果
     */
    public boolean saveXiaomiAdCodeToAdSidManage(AdvAdcodeXiaomiInfo adCodeInfo) {
        ExtendAdsidVo app = new ExtendAdsidVo();
        try {
            //平台_广告源类型_产品id_扩展名
            String adType = ADTYPE_MAP.get(adCodeInfo.getAdType());
            String sdkAdType = adCodeInfo.getSdk_ad_type();
            String appid = adCodeInfo.getAppid();
            String adExtentionName = adCodeInfo.getAdExtensionName();
            String adsid = PLATFORM_XIAOMI_NAME + "_" + sdkAdType + "_" + appid + "_" + adExtentionName;
            //adsid
            app.setAdsid(adsid);
            //广告平台
            app.setAgent(PLATFORM_XIAOMI_NAME);
            //广告位id mobvista sdk_code为 unit_id
            app.setSdk_code(adCodeInfo.getPlacementid());
            //广告平台产品id
            app.setSdk_appid(adCodeInfo.getTappid());
            //SDK广告类型
            app.setSdk_adtype(sdkAdType);
            //广告平台广告类型
            app.setAdpos_type(adType);
            //备注
            app.setNote(adCodeInfo.getRemark());
            //动能产品id
            app.setAppid(adCodeInfo.getAppid());
            //子渠道
            app.setCha_id(adCodeInfo.getChannel());
            //创建人
            app.setCuser(adCodeInfo.getCreateUser());
            //广告使用类型
            app.setOpen_type(adCodeInfo.getOpen_type());
            // bidding模式
            app.setBidding(adCodeInfo.getBidding());
            // 区分应用内外
            app.setOut(adCodeInfo.getOut());
            //自定义参数
            app.setParams(adCodeInfo.getParams());
            int result = adv2Mapper.insertDnExtendAdsidManageV2(app);
            if (result > 0) {
                return true;
            }
        } catch (Exception e) {
            log.error("saveXiaomiAdCodeToAdSidManage error:", e);
        }
        return false;
    }

    /**
     * 保存vivo创建的广告位至广告配置表 dn_extend_adconfig
     *
     * @param adCodeInfo
     * @return
     */
    public boolean saveXiaomiAdcodeToDnExtendAdconfig(AdvAdcodeXiaomiInfo adCodeInfo) {
        ExtendAdconfigVo config = new ExtendAdconfigVo();

        String appid = adCodeInfo.getAppid();
        config.setAppid(appid);
        config.setIs_newuser("all");
        config.setUser_group("all");
        config.setAdpos_type(adCodeInfo.getOpen_type());
        config.setStrategy(adCodeInfo.getStrategy());

        //String agent = PLATFORM_XIAOMI_NAME;
        String sdkAdType = adCodeInfo.getSdk_ad_type();
        String adExtentionName = adCodeInfo.getAdExtensionName();
        String adsid = PLATFORM_XIAOMI_NAME + "_" + sdkAdType + "_" + appid + "_" + adExtentionName;

        config.setCha_id(adCodeInfo.getChannel());
        config.setAdsid(adsid);
        config.setStatu("1");

        config.setEcpm(new BigDecimal("0"));
        String ecpm = adCodeInfo.getEcpm();
        if (!BlankUtils.checkBlank(ecpm) && BlankUtils.isNumeric(ecpm)) {
            config.setEcpm(new BigDecimal(ecpm));
        }
        // 符合填入规则 0 - 100 正整数填入， 否则填入100
        String configRate = adCodeInfo.getConfigRate();
        if (!BlankUtils.checkBlank(configRate) && StringUtils.isNumeric(configRate)) {
            int rate = Integer.parseInt(configRate);
            if (rate >= 0 && rate <= 100) {
                config.setRate(rate);
            } else {
                config.setRate(100);
            }
        } else {
            config.setRate(100);
        }
        config.setPriority(0);
        config.setCuser(adCodeInfo.getCreateUser());
        return adv2Mapper.insertDnExtendAdconfig(config) > 0;

    }




}
