package com.wbgame.service.adv2.adcode;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wbgame.common.ReturnJson;
import com.wbgame.controller.inter.pojo.EncryptUtil;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.adv2.AdvCommonAdCodeMapper;
import com.wbgame.pojo.adv2.*;
import com.wbgame.service.adv2.AdCodeConfigService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExcelUtils;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.MD5Util;
import com.wbgame.utils.baidu.UbapiClient;
import com.wbgame.utils.baidu.UbapiClientSample;
import io.netty.handler.ssl.PemPrivateKey;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jce.provider.JDKMessageDigest;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.util.*;
import java.util.stream.Collectors;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;
import static com.wbgame.utils.baidu.UbapiClientSample.PRIVATE_KEY;

/**
 * <AUTHOR>
 * @date 2023/10/23
 * @description 百度广告位相关
 **/
@Service
@Slf4j
public class BaiduAdcodeService {

    public static final String BAIDU_NAME = "baidu";
    public static final String BASE_URL = "https://ubapi.baidu.com";
    public static final String ADCODE_LIST_URL = "/ssp/1/sspservice/appadpos/app/adpos/page-query";
    public static final String ADCODE_CREATE_URL = "/ssp/1/sspservice/appadpos/app/adpos/create";
    public static final Set<Integer> INTERSTITIAL_STYLE_TYPES1 = Sets.newHashSet(1,2,4,5);
    public static final int INTERSTITIAL_STYLE_TYPES2 = 3;

    public static final Map<Integer, String> ADTYPE_MAP = new HashMap<Integer, String>(){{
        put(33,"splash");
        put(36,"msg");
        put(44,"video");
        put(34,"plaque");
    }};

    private Map<String, UbapiClient> clientMap = new HashMap<>();

    @Resource
    private AdCodeConfigService adCodeConfigServiceImpl;
    @Resource
    private AdvCommonAdCodeMapper advCommonAdCodeMapper;
    @Resource
    private Adv2Mapper adv2Mapper;
    public JSONObject createAdcode(BaiduAdCodeVo vo) {
        StringBuilder errorMsg = new StringBuilder();

        //先判断当前广告位id是否已经存在
        String sdkAdType= vo.getSdk_ad_type();
        int appid = Integer.parseInt(vo.getAppid());
        String adExtentionName = vo.getAdExtensionName();
        String adsid = BAIDU_NAME +"_"+sdkAdType+"_"+appid+"_"+adExtentionName;
        // 广告源中查询
        ExtendAdsidVo adsidVo = adCodeConfigServiceImpl.getDnAdSid(adsid);
        if (adsidVo!=null){
            JSONObject map = new JSONObject();
            map.put("ret", 0);
            map.put("msg", "创建广告位失败,原因:生成规则:广告平台_广告位类型_产品id_扩展名生成的ad-sid 已经在广告源表存在");
            return map;
        }

        String user = LOGIN_USER_NAME.get();
        vo.setCreateUser(user);
        vo.setModifyUser(user);
        //产品对应配置信息
        AdCodeAccountVo account = new AdCodeAccountVo();
        account.setAppid(String.valueOf(vo.getAppid()));

        if (vo.getApp_sid() ==null && BlankUtils.isBlank(vo.getChannel())) {
            JSONObject map = new JSONObject();
            map.put("ret", 0);
            map.put("msg", "创建广告位失败,原因:平台产品id参数未传");
            return map;
        }
        if (vo.getApp_sid()!= null) {
            account.setTappid(String.valueOf(vo.getApp_sid()));
        }
        account.setChannel(vo.getChannel());

        List<AdCodeAccountVo> list = adCodeConfigServiceImpl.getAdCodeAccountList(account);
        account = list.stream().filter(t->t.getAppid().equals(String.valueOf(vo.getAppid()))
                &&BAIDU_NAME.equals(t.getPlatform())).findFirst().orElse(null);
        if(account==null) {
            JSONObject map = new JSONObject();
            map.put("ret", 0);
            map.put("msg", "创建广告位失败,原因:该产品未配置变现平台账号");
            return map;
        }
        //设置平台产品id
        vo.setApp_sid(account.getTappid());

        //先创建广告位
        JSONObject adCodeRet = createAdCode(vo,account);
        if (adCodeRet.getIntValue("ret") == 0) {
            return adCodeRet;
        }
        // 放入placement id
        vo.setPlacementid(adCodeRet.getString("msg"));

        errorMsg.append("广告位创建成功");

//        // 写入adcode表、写入广告源表、写入广告配置表
        int adCodeNum = advCommonAdCodeMapper.insertBaiduAdCode(vo);
        if (adCodeNum < 1) {
            String msg = "写入adcode表失败";
            errorMsg.append(",").append(msg);
        }
        if (!saveAdCodeToAdSidManage(vo, account)) {
            String msg = "写入广告源表失败";
            errorMsg.append(",").append(msg);
        }
        if (!saveAdcodeToDnExtendAdconfig(vo)) {
            String msg = "写入广告配置表失败";
            errorMsg.append(",").append(msg);
        }

        return ReturnJson.successJSON(errorMsg.toString());
    }

    public PageInfo<BaiduAdCodeVo> findBaiduAdCode(BaiduAdCodeVo vo, int start, int limit) {
        PageHelper.startPage(start, limit);
        List<BaiduAdCodeVo> baiduAdCodeVos = advCommonAdCodeMapper.selectBaiduAdCodeList(vo);

        return new PageInfo<>(baiduAdCodeVos);
    }

    private boolean saveAdcodeToDnExtendAdconfig(BaiduAdCodeVo vo) {
        ExtendAdconfigVo config = new ExtendAdconfigVo();
        // 子渠道为 apple 则不填入
        config.setCha_id("apple".equals(vo.getChannel()) || "csj".equals(vo.getChannel())
                ? "" : vo.getChannel());

        String appid = vo.getAppid().toString();
        config.setAppid(appid);
        config.setIs_newuser("all");
        config.setUser_group("all");
        config.setAdpos_type(vo.getOpen_type());
        config.setStrategy(vo.getStrategy());

        String agent = BAIDU_NAME;
        String sdkAdType = vo.getSdk_ad_type();
        String adExtentionName = vo.getAdExtensionName();
        String adsid = agent+"_"+sdkAdType+"_"+appid+"_"+adExtentionName;

        config.setAdsid(adsid);
        config.setStatu("1");
        //策略带rate 默认为0 其他默认值为3
        if (vo.getCpm() == null || vo.getCpm()==0){
            config.setEcpm(new BigDecimal("1"));
        }else {
            if (vo.getStrategy().contains("rate")){
                config.setEcpm(new BigDecimal("0"));
            }else {
                config.setEcpm(new BigDecimal(vo.getCpm().toString()));
            }
        }
        //2022-09-01 开屏、banner、msg的无价格广告，在广告配置页面生成的预估ecpm默认为0；
        // 插屏、视频的无价格广告，在广告配置页面生成的预估ecpm默认为3  mobvista指定改为5
        String[] ecpm0 = {"splash","banner","msg"};
        if (Arrays.asList(ecpm0).contains(config.getAdpos_type())){
            if (vo.getCpm() == null){
                config.setEcpm(new BigDecimal("0"));
            }
        }

        String[] ecpm3 = {"plaque","video"};
        if (Arrays.asList(ecpm3).contains(config.getAdpos_type())){
            if (vo.getCpm() == null){
                config.setEcpm(new BigDecimal("3"));
            }
        }

        if (vo.getPrice_type() == 2) {
            config.setEcpm(new BigDecimal(3));
        }

        config.setPriority(0);
        config.setRate(100);
        config.setCuser(vo.getCreateUser());

        return adv2Mapper.insertDnExtendAdconfig(config) > 0;
    }

    public static final Map<Integer, String> ADSID_BID_MAP = new HashMap<Integer, String>(){{
        put(1, "0");
        put(2, "1");
    }};
    private boolean saveAdCodeToAdSidManage(BaiduAdCodeVo vo, AdCodeAccountVo account) {
        ExtendAdsidVo app = new ExtendAdsidVo();
        try {
            //平台_广告源类型_产品id_扩展名
            String adType= ADTYPE_MAP.get(vo.getAd_type());
            String sdkAdType = vo.getSdk_ad_type();
            String appid = account.getAppid();
            String adExtentionName = vo.getAdExtensionName();
            String adsid = BAIDU_NAME +"_"+sdkAdType+"_"+appid+"_"+adExtentionName;
            //adsid
            app.setAdsid(adsid);
            //广告平台
            app.setAgent(BAIDU_NAME);
            //广告位id mobvista sdk_code为 unit_id
            app.setSdk_code(vo.getPlacementid());
            //广告平台产品id
            app.setSdk_appid(account.getTappid());

            // app.setSdk_appkey(MOBVISTA_SDK_APPKEY);  不知道干什么用的
            //SDK广告类型
            app.setSdk_adtype(sdkAdType);
            //广告平台广告类型
            app.setAdpos_type(adType);
            //备注
            app.setNote(vo.getRemark());
            //动能产品id
            app.setAppid(account.getAppid());
            //子渠道
            app.setCha_id(vo.getChannel());
            //创建人
            app.setCuser(vo.getCreateUser());
            //cpm
            Integer targetPrice = vo.getCpm();
            app.setCpmFloor(targetPrice == null? "0" : targetPrice.toString());
            //广告使用类型
            app.setOpen_type(vo.getOpen_type());
            //设置bidding模式
            app.setBidding(ADSID_BID_MAP.get(vo.getPrice_type()));
            app.setParams(vo.getParams());
            int result = adv2Mapper.insertDnExtendAdsidManage(app);
            if (result>0){
                return true;
            }
        }catch (Exception e){
            log.error("saveOppoAdCodeToAdSidManage error:",e);
        }
        return false;
    }

    private JSONObject createAdCode(BaiduAdCodeVo vo, AdCodeAccountVo account) {
        // 通用字段判断
        HashMap<String, Object> params = new HashMap<>();
        params.put("app_sid", vo.getApp_sid());
        Integer adType = vo.getAd_type();
        params.put("ad_type", adType);
        if (BlankUtils.isNotBlank(vo.getAd_name())) {
            params.put("ad_name", vo.getAd_name());
        }
        if (BlankUtils.isNotBlank(vo.getAd_tags())) {
            params.put("ad_tags", Lists.newArrayList(vo.getAd_tags().split(",")));
        }
        if (BlankUtils.isNotBlank(vo.getAd_tags())) {
            params.put("ad_tags", Lists.newArrayList(vo.getAd_tags().split(",")));
        }
//        if (vo.getChannel_id() != null) {
//
//        }
        if (vo.getScene_tag() != null) {
            params.put("scene_tag", vo.getScene_tag());
        }

        params.put("price_type", vo.getPrice_type());
        if (vo.getPrice_type() == 1) {
            params.put("cpm", vo.getCpm() == null? 0: vo.getCpm());
        }

        HashMap<String, Object> map = new HashMap<>();
        //  根据代码位类型配置 ad_info
        if (adType == 36) {
            // 信息流基本字段
            Integer type = vo.getInfo_flow_style_control_type();
            map.put("info_flow_style_control_type", type);

            if (type == 1) {
                // 优选
                if (BlankUtils.isBlank(vo.getInfo_flow_material_types())) {
                    return ReturnJson.failed("选择优选模板时,信息流物料类型必填");
                }
                List<Integer> info_flow_material_types = Arrays.stream(vo.getInfo_flow_material_types()
                                .split(",")).map(BlankUtils::getInt).collect(Collectors.toList());
                map.put("info_flow_material_types", info_flow_material_types);

                List<Integer> info_flow_templates = Arrays.stream(vo.getInfo_flow_templates().split(","))
                        .map(BlankUtils::getInt).collect(Collectors.toList());
                if (info_flow_material_types.contains(7)) {
                    // 16，17 不支持视频物料
                    if (info_flow_templates.contains(16) || info_flow_templates.contains(17)) {
                        return ReturnJson.failed("16，17模板 不支持视频物料");
                    }
                }
            } else if (type == 2) {
                // 自渲染
                HashMap<String, Object> temp = new HashMap<>();
                String styleTypes = vo.getStyle_types();
                if (BlankUtils.isNotBlank(styleTypes)) {
                    List<Integer> collect = Arrays.stream(styleTypes.split(","))
                            .map(BlankUtils::getInt).collect(Collectors.toList());
                    temp.put("style_types", collect);
                }
                String sizes = vo.getSizes();
                if (BlankUtils.isNotBlank(sizes)) {
                    List<Integer> collect = Arrays.stream(sizes.split(","))
                            .map(BlankUtils::getInt).collect(Collectors.toList());
                    temp.put("sizes", collect);
                }

                if (!CollectionUtils.isEmpty(temp)) {
                    map.put("info_flow_self_render", temp);
                }

            } else if (type == 3) {
                // 返回元素 暂时不做
                return ReturnJson.failed("暂时不支持 信息流返回原素 的配置");

            }
        } else if (adType == 33) {
            // 开屏
            if (BlankUtils.isNotBlank(vo.getSplash_material_types())) {
                List<Integer> collect = Arrays.stream(vo.getSplash_material_types().split(","))
                        .map(BlankUtils::getInt).collect(Collectors.toList());
                map.put("splash_material_types", collect);
            }
            if (vo.getSplash_show_control() != null) {
                map.put("splash_show_control", vo.getSplash_show_control());
            }
        } else if (adType == 34) {
            // 插屏
            Integer scene = vo.getInterstitial_ad_scene();
            map.put("interstitial_ad_scene", scene);

            if (BlankUtils.isNotBlank(vo.getInterstitial_style_types())) {
                List<Integer> interstitial_style_types = Arrays.stream(vo.getInterstitial_style_types().split(","))
                        .map(BlankUtils::getInt).collect(Collectors.toList());

                HashSet<Integer> collect = new HashSet<>(interstitial_style_types);

                if (scene == 1) {
                    // 竖版 没有3
                    collect.removeAll(INTERSTITIAL_STYLE_TYPES1);
                    if (CollectionUtils.isEmpty(collect)) {
                        map.put("interstitial_style_types", interstitial_style_types);
                    } else {
                        return ReturnJson.failed("竖版 插屏广告样式没有3");
                    }
                } else {
                    // 横板只有3
                    collect.remove(INTERSTITIAL_STYLE_TYPES2);
                    if (CollectionUtils.isEmpty(collect)) {
                        map.put("interstitial_style_types", interstitial_style_types);
                    } else {
                        return ReturnJson.failed("横板 插屏广告样式只有3");
                    }
                }
            }

            if (BlankUtils.isNotBlank(vo.getInterstitial_material_types())) {
                List<Integer> collect = Arrays.stream(vo.getInterstitial_material_types().split(","))
                        .map(BlankUtils::getInt).collect(Collectors.toList());
                map.put("interstitial_material_types", collect);
            }
        } else if (adType == 44) {
            // 激励视频
            Integer control = vo.getReward_video_return_control();
            if (control != null) {
                map.put("reward_video_return_control", control);
                if (control == 1) {
                    if (BlankUtils.isNotBlank(vo.getReward_video_return_url())) {
                        map.put("reward_video_return_url", vo.getReward_video_return_url());
                    }
                }
            }

            if (vo.getReward_video_voice_control() != null) {
                map.put("reward_video_voice_control", vo.getReward_video_voice_control());
            }
        } else {
            return ReturnJson.failed("暂不支持的代码位类型");
        }
        if (!CollectionUtils.isEmpty(map)) {
            params.put("ad_info", map);
        }

        String tsecrect = account.getTsecrect();
        UbapiClient ubapiClient = clientMap.get(tsecrect);
        if (ubapiClient == null) {
            ubapiClient =  new UbapiClient(BASE_URL, tsecrect, UbapiClientSample.PRIVATE_KEY);
            clientMap.put(tsecrect, ubapiClient);
        }

        String jsonString = JSONObject.toJSONString(params);
        log.info("百度创建api参数: {}", jsonString);

        try {
            HttpResponse response = ubapiClient.post(ADCODE_CREATE_URL,
                    "application/json", jsonString.getBytes());
            HttpEntity entity = response.getEntity();
            String res = EntityUtils.toString(entity, "UTF8");
            log.info("百度创建api返回: {}", res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (jsonObject.getIntValue("code") == 0) {
                return ReturnJson.successJSON(jsonObject.getJSONObject("data").getString("tu_id"));
            } else {
                return ReturnJson.failed("平台返回" + jsonObject.getString("message"));
            }
        } catch (IOException e) {
            log.error("请求创建失败:", e);
            return ReturnJson.failed("创建异常: " + e.getMessage());
        }
    }

    public Object getAdCodeList() {
        String accessKey = "xFa9K3Rz2VChtT7d";
        HashMap<String, Object> params = new HashMap<>();
        params.put("page_no", 1);
        params.put("page_size", 100);
        String para = JSONObject.toJSONString(params);

        UbapiClient client = new UbapiClient(BASE_URL, accessKey, UbapiClientSample.PRIVATE_KEY);
        HttpResponse response = null;
        try {
            response = client.post(ADCODE_LIST_URL, "application/json", para.getBytes());
            HttpEntity entity = response.getEntity();
            String res = EntityUtils.toString(entity, "UTF8");
            return res;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public static void main(String[] args) throws Exception {
        String accessKey = "xFa9K3Rz2VChtT7d";
        HashMap<String, Object> params = new HashMap<>();
        params.put("page_no", 1);
        params.put("page_size", 100);
        String para = JSONObject.toJSONString(params);

        UbapiClient client = new UbapiClient(BASE_URL, accessKey, UbapiClientSample.PRIVATE_KEY);
        HttpResponse response = client.post(ADCODE_LIST_URL, "application/json", para.getBytes());
        HttpEntity entity = response.getEntity();
        String res = EntityUtils.toString(entity, "UTF8");
        log.info("返回信息:{}", res);
    }

    public static final Map<String, Integer> BAIDU_AD_TYPE_MAP = new HashMap<String, Integer>(){{
       put("开屏", 33);
       put("插屏", 34);
       put("信息流", 36);
       put("激励视频", 44);
    }};

    public Object batchImport(MultipartFile file) throws IOException {
        List<ArrayList<String>> arrayLists = null;
        try {
            arrayLists = readExcel(file);
        } catch (BiffException e) {
            throw new RuntimeException(e);
        }
        int index = 0;
        if (!CollectionUtils.isEmpty(arrayLists)) {
            StringBuilder sb = new StringBuilder();
            for (ArrayList<String> column : arrayLists) {
                index ++;
                BaiduAdCodeVo vo = new BaiduAdCodeVo();
                JSONObject ret = buildVo(index, column, vo);
                if (ret.getIntValue("ret") != 1) {
                    sb.append(ret.getString("msg") == null? ret.getString("massage"):ret.getString("msg"))
                            .append("<br>");
                    continue;
                }
                vo = ret.getObject("data", BaiduAdCodeVo.class);
                JSONObject adcode = createAdcode(vo);
                if (adcode.getIntValue("ret") != 1) {
                    sb.append(ret.getString("msg")).append("<br>");
                }
            }
            return ReturnJson.success(sb.length() == 0? "成功": sb.toString());
        }
        return ReturnJson.failed("没有有效数据");
    }

    private List<ArrayList<String>> readExcel(MultipartFile file) throws IOException, BiffException {
        if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {

            ArrayList<ArrayList<String>> arrayLists = new ArrayList<>();
            Workbook workbook = Workbook.getWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheet(0);

            int column = sheet.getColumns();
            int row = sheet.getRows();
            for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                    continue;
                }
                ArrayList<String> strings = new ArrayList<>();
                for (int c = 0; c < column; c++) {
                    strings.add(sheet.getCell(c, r).getContents());
                }
                arrayLists.add(strings);
            }

            return arrayLists;
        }
        return null;
    }

    //SDK广告源类型
    private static Map<String, String> SDK_AD_TYPE_MAP = new HashMap<String, String>() {{
        put("开屏", "splash");
        put("原生开屏", "natSplash");
        put("普通banner/模板", "banner");
        put("banner自渲染", "natBanner");
        put("信息流模板", "yuans");
        put("信息流自渲染", "msg");
        put("普通插屏/模板", "plaque");
        put("自渲染插屏", "natPlaque");
        put("插屏视频", "plaqueVideo");
        put("视频", "video");
        put("视频自渲染", "natVideo");
        put("icon", "icon");
    }};
    //广告使用类型
    private static Map<String, String> OPEN_TYPE_MAP = new HashMap<String, String>() {{
        put("信息流-msg", "msg");
        put("banner-banner", "banner");
        put("开屏-splash", "splash");
        put("视频-video", "video");
        put("插屏-plaque", "plaque");
    }};

    @Nullable
    private static JSONObject buildVo(int index, ArrayList<String> column, BaiduAdCodeVo vo) {
        try {
            vo.setAppid(column.get(0));
            vo.setChannel(column.get(1));
            vo.setSdk_ad_type(SDK_AD_TYPE_MAP.get(column.get(2)));
            vo.setOpen_type(OPEN_TYPE_MAP.get(column.get(3)));
            vo.setAdExtensionName(column.get(4));
            vo.setStrategy(column.get(5));
            vo.setRemark(column.get(6));
            vo.setAd_name(column.get(7));
            vo.setApp_sid(column.get(8));
            vo.setAd_type(BAIDU_AD_TYPE_MAP.get(column.get(9)));
            vo.setPrice_type(BlankUtils.getInt(column.get(10)));
            if (vo.getPrice_type() == 0) {
                return ReturnJson.failed(String.format("第%d行，竞价方式必填", index));
            }
            vo.setAd_tags(column.get(11));
            vo.setCpm(BlankUtils.getInt(column.get(12)));
            if (BlankUtils.isNotBlank(column.get(13))) {
                vo.setInfo_flow_style_control_type(BlankUtils.getInt(column.get(13)));
            }
            vo.setInfo_flow_material_types(column.get(14));
            vo.setInfo_flow_templates(column.get(15));
            vo.setStyle_types(column.get(16));
            vo.setSizes(column.get(17));
            vo.setSplash_material_types(column.get(18));
            if (BlankUtils.isNotBlank(column.get(19))) {
                vo.setSplash_show_control(BlankUtils.getInt(column.get(19)));
            }
            if (BlankUtils.isNotBlank(column.get(20))) {
                vo.setInterstitial_ad_scene(BlankUtils.getInt(column.get(20)));
            }
            vo.setInterstitial_style_types(column.get(21));
            vo.setInterstitial_material_types(column.get(22));
            if (BlankUtils.isNotBlank(column.get(23))) {
                vo.setReward_video_return_control(BlankUtils.getInt(column.get(23)));
            }
            vo.setReward_video_return_url(column.get(24));
            if (BlankUtils.isNotBlank(column.get(25))) {
                vo.setReward_video_voice_control(BlankUtils.getInt(column.get(25)));
            }
            return ReturnJson.successJSON(vo);
        } catch (Exception e) {
            log.error("unexpect error: ", e);
            return ReturnJson.failed(String.format("第%d行，配置出错", index));
        }
    }
}
