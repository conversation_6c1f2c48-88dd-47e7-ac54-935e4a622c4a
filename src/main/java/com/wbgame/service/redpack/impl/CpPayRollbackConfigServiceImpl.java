package com.wbgame.service.redpack.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.redpack.CpPayRollbackConfigMapper;
import com.wbgame.pojo.mobile.CpPayRollbackConfig;
import com.wbgame.pojo.mobile.CpPayRollbackConfigVO;
import com.wbgame.service.redpack.ICpPayRollbackConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2023/01/16 016
 * @class: CpPayRollbackConfigServiceImpl
 * @description:
 */
@Service
public class CpPayRollbackConfigServiceImpl implements ICpPayRollbackConfigService {

    @Autowired
    private CpPayRollbackConfigMapper payRollbackConfigMapper;

    @Override
    public Result<Integer> deleteCpPayRollbackConfig(List<String> appidList) {

        return ObjectUtils.isEmpty(appidList) ? ResultUtils.failure("参数错误")
                : ResultUtils.success(payRollbackConfigMapper.deleteCpPayRollbackConfig(appidList));
    }

    @Override
    public Result<Integer> insertCpPayRollbackConfig(CpPayRollbackConfig record) {


        if (payRollbackConfigMapper.selectAppIdExist(record.getAppid()) != null) {

            return ResultUtils.failure("该产品已配置");
        }

        payRollbackConfigMapper.insertCpPayRollbackConfig(record);
        return ResultUtils.success();
    }

    @Override
    public Result<PageResult<CpPayRollbackConfigVO>> selectCpPayRollbackConfig(CpPayRollbackConfig example) {

        PageHelper.startPage(example.getStart(), example.getLimit());
        List<CpPayRollbackConfigVO> list = payRollbackConfigMapper.selectCpPayRollbackConfig(example);
        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    public Result<Integer> updateCpPayRollbackConfig(CpPayRollbackConfig record) {

        payRollbackConfigMapper.updateCpPayRollbackConfig(record);

        return ResultUtils.success();
    }
}
