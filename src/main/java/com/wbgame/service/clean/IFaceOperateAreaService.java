package com.wbgame.service.clean;


import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.face.FaceOperateArea;
import com.wbgame.pojo.clean.face.FaceOpreateSort;
import com.wbgame.pojo.clean.face.FaceProductConfig;
import com.wbgame.pojo.clean.face.FaceProductLinArea;
import com.wbgame.utils.PageResult;

import java.util.List;
import java.util.Map;

public interface IFaceOperateAreaService {

    Result<Integer> deleteFaceOperateArea(List<Integer> idList);

    Result<Integer> insertFaceOperateArea(FaceOperateArea record);

    Result<PageResult<FaceOperateArea>> selectFaceOperateArea(FaceOperateArea example);

    Result<Integer> updateFaceOperateArea(FaceOperateArea record);


    /**
     * 商品排序
     * @param sortList
     * @return
     */
    Result<Integer> updateSort(List<FaceOpreateSort> sortList);

    /**
     * 编辑商品对应的运营地区
     * @param productLinArea
     * @return
     */
    Result<Integer> insertFaceOpreateSort(FaceProductLinArea productLinArea, String userName);

    /**
     * 根据渠道获取渠道所对应的地区(下拉列表)
     */
    Result<Map<String, List<FaceProductConfig>>> getAreaListByCha();

    /**
     * 根据地区获取对应的数据
     */
    Result<List<FaceProductConfig>> selectConfigByArea(FaceProductConfig area);

    /**
     * 根据商品id获取分类地区
     */
    List<FaceOpreateSort> selectOpreateSortByProductId(Integer productId);

    /**
     * 根据渠道查询还未分配运营地区的商品
     */
    Result<List<FaceProductConfig>> selectProductByChannelDownArea(String channel, String area);

    Result<Integer> batchAddProductToSort(List<FaceOpreateSort> sortList);
}