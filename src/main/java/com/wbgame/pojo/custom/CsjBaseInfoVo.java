package com.wbgame.pojo.custom;

public class CsjBaseInfoVo {
	
	private String id; // 主键
	private String app; // 系统应用Id
	private String ad_platform; // 变现平台
	private String account; // 广告账号
	private String ad_type; // 广告类型
	private String day; // 日期
	private String placementId; // 广告位ID
	private int requests; // 请求数
	private int filled; // 填充数
	private int impressions; // 展示数
	private int clicks; // 点击数
	private String earnings; // 预估收益
	private String channelType; // 投放类型（渠道类型）
	private String channel; // 投放子渠道
	private String media; // 投放媒体
	private String channelMark; // 渠道标识
	private String createTime; // 创建时间
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getApp() {
		return app;
	}
	public void setApp(String app) {
		this.app = app;
	}
	public String getAd_platform() {
		return ad_platform;
	}
	public void setAd_platform(String ad_platform) {
		this.ad_platform = ad_platform;
	}
	public String getAccount() {
		return account;
	}
	public void setAccount(String account) {
		this.account = account;
	}
	public String getAd_type() {
		return ad_type;
	}
	public void setAd_type(String ad_type) {
		this.ad_type = ad_type;
	}
	public String getDay() {
		return day;
	}
	public void setDay(String day) {
		this.day = day;
	}
	public String getPlacementId() {
		return placementId;
	}
	public void setPlacementId(String placementId) {
		this.placementId = placementId;
	}
	public int getRequests() {
		return requests;
	}
	public void setRequests(int requests) {
		this.requests = requests;
	}
	public int getFilled() {
		return filled;
	}
	public void setFilled(int filled) {
		this.filled = filled;
	}
	public int getImpressions() {
		return impressions;
	}
	public void setImpressions(int impressions) {
		this.impressions = impressions;
	}
	public int getClicks() {
		return clicks;
	}
	public void setClicks(int clicks) {
		this.clicks = clicks;
	}
	public String getEarnings() {
		return earnings;
	}
	public void setEarnings(String earnings) {
		this.earnings = earnings;
	}
	public String getChannelType() {
		return channelType;
	}
	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	public String getMedia() {
		return media;
	}
	public void setMedia(String media) {
		this.media = media;
	}
	public String getChannelMark() {
		return channelMark;
	}
	public void setChannelMark(String channelMark) {
		this.channelMark = channelMark;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	
}
