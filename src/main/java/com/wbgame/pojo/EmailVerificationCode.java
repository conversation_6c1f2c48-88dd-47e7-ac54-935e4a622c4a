package com.wbgame.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> Assistant
 * @Description 邮件验证码实体类
 * @Date 2025/01/16
 */
@Data
public class EmailVerificationCode {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 验证邮箱
     */
    private String email;

    /**
     * 邮件ID，唯一标识
     */
    private String messageId;

    /**
     * 邮件标题
     */
    private String subject;

    /**
     * 邮件内容
     */
    private String content;

    /**
     * 解析出的验证码
     */
    private String verificationCode;

    /**
     * 平台：华为/小米/荣耀
     */
    private String platform;

    /**
     * 账号信息：如华为账号、荣耀账号、小米用户等
     */
    private String accountInfo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
