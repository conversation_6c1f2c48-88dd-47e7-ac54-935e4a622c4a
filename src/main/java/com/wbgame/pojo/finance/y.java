package com.wbgame.pojo.finance;

import java.lang.reflect.Field;

public class y {

    public static void main(String[] args) {


        for (String string : getFiledName(new FinancePreIncome())) {
            System.out.println("headerMap.put(\"" + string + "\", \"" + string + "\");");
        }
        System.out.println();
        System.out.println("contentMap = new LinkedHashMap<String, Object>();");
        for (String string : getFiledName(new FinancePreIncome())) {

            System.out.println("contentMap.put(\"" + string + "\", temp.get" + string.substring(0, 1).toUpperCase() + string.substring(1) + "());");
        }
    }


    /**
     * 获得所有属性数组
     *
     * @param o
     * @return
     */
    private static String[] getFiledName(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        String[] fieldNames = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            fieldNames[i] = fields[i].getName();
        }
        return fieldNames;
    }
}
