package com.wbgame.pojo.finance;

/**
 * 财务系统-支出系统-联运CPS支出
 *
 * <AUTHOR>
 */
public class FinanceCpsCost {

    /**
     * 年
     */
    private String year;

    /**
     * 月
     */
    private String month;

    /**
     * appid
     */
    private String appid;

    /**
     * cps渠道
     */
    private String cps;

    /**
     * 渠道公司名称
     */
    private String company;

    /**
     * 我方合同主体
     */
    private String contract;

    /**
     * 收入
     */
    private String income;

    /**
     * 分成比例
     */
    private String ratio;

    /**
     * 税费扣除率
     */
    private String taxes;

    /**
     * cps分成
     */
    private String cpsratio;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getCps() {
        return cps;
    }

    public void setCps(String cps) {
        this.cps = cps;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company == null ? null : company.trim();
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract == null ? null : contract.trim();
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income == null ? null : income.trim();
    }

    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio == null ? null : ratio.trim();
    }

    public String getTaxes() {
        return taxes;
    }

    public void setTaxes(String taxes) {
        this.taxes = taxes == null ? null : taxes.trim();
    }

    public String getCpsratio() {
        return cpsratio;
    }

    public void setCpsratio(String cpsratio) {
        this.cpsratio = cpsratio == null ? null : cpsratio.trim();
    }
}