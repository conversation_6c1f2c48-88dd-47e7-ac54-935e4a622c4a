package com.wbgame.pojo.push;

public class ApkProductKeepVo {
	
	private String mmdate; // 日期
	private String product_id; // 产品ID
	private String projectid; // 项目ID
	private String usernum; // 新增用户数
	private String keep1; // 留存率 百分数0.67
	private String keep2;
	private String keep3;
	private String keep4;
	private String keep5;
	private String keep6;
	private String keep7;
	private String keep8;
	private String keep9;
	private String keep10;
	private String keep11;
	private String keep12;
	private String keep13;
	private String keep14;
	private String keep15;
	private String keep16;
	private String keep17;
	private String keep18;
	private String keep19;
	private String keep20;
	private String keep21;
	private String keep22;
	private String keep23;
	private String keep24;
	private String keep25;
	private String keep26;
	private String keep27;
	private String keep28;
	private String keep29;
	private String keep30;
	
	private String param1;
	private String param2;
	
	public String getMmdate() {
		return mmdate;
	}
	public void setMmdate(String mmdate) {
		this.mmdate = mmdate;
	}
	public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public String getProjectid() {
		return projectid;
	}
	public void setProjectid(String projectid) {
		this.projectid = projectid;
	}
	public String getUsernum() {
		return usernum;
	}
	public void setUsernum(String usernum) {
		this.usernum = usernum;
	}
	public String getKeep1() {
		return keep1;
	}
	public void setKeep1(String keep1) {
		this.keep1 = keep1;
	}
	public String getKeep2() {
		return keep2;
	}
	public void setKeep2(String keep2) {
		this.keep2 = keep2;
	}
	public String getKeep3() {
		return keep3;
	}
	public void setKeep3(String keep3) {
		this.keep3 = keep3;
	}
	public String getKeep4() {
		return keep4;
	}
	public void setKeep4(String keep4) {
		this.keep4 = keep4;
	}
	public String getKeep5() {
		return keep5;
	}
	public void setKeep5(String keep5) {
		this.keep5 = keep5;
	}
	public String getKeep6() {
		return keep6;
	}
	public void setKeep6(String keep6) {
		this.keep6 = keep6;
	}
	public String getKeep7() {
		return keep7;
	}
	public void setKeep7(String keep7) {
		this.keep7 = keep7;
	}
	public String getKeep8() {
		return keep8;
	}
	public void setKeep8(String keep8) {
		this.keep8 = keep8;
	}
	public String getKeep9() {
		return keep9;
	}
	public void setKeep9(String keep9) {
		this.keep9 = keep9;
	}
	public String getKeep10() {
		return keep10;
	}
	public void setKeep10(String keep10) {
		this.keep10 = keep10;
	}
	public String getKeep11() {
		return keep11;
	}
	public void setKeep11(String keep11) {
		this.keep11 = keep11;
	}
	public String getKeep12() {
		return keep12;
	}
	public void setKeep12(String keep12) {
		this.keep12 = keep12;
	}
	public String getKeep13() {
		return keep13;
	}
	public void setKeep13(String keep13) {
		this.keep13 = keep13;
	}
	public String getKeep14() {
		return keep14;
	}
	public void setKeep14(String keep14) {
		this.keep14 = keep14;
	}
	public String getKeep15() {
		return keep15;
	}
	public void setKeep15(String keep15) {
		this.keep15 = keep15;
	}
	public String getKeep16() {
		return keep16;
	}
	public void setKeep16(String keep16) {
		this.keep16 = keep16;
	}
	public String getKeep17() {
		return keep17;
	}
	public void setKeep17(String keep17) {
		this.keep17 = keep17;
	}
	public String getKeep18() {
		return keep18;
	}
	public void setKeep18(String keep18) {
		this.keep18 = keep18;
	}
	public String getKeep19() {
		return keep19;
	}
	public void setKeep19(String keep19) {
		this.keep19 = keep19;
	}
	public String getKeep20() {
		return keep20;
	}
	public void setKeep20(String keep20) {
		this.keep20 = keep20;
	}
	public String getKeep21() {
		return keep21;
	}
	public void setKeep21(String keep21) {
		this.keep21 = keep21;
	}
	public String getKeep22() {
		return keep22;
	}
	public void setKeep22(String keep22) {
		this.keep22 = keep22;
	}
	public String getKeep23() {
		return keep23;
	}
	public void setKeep23(String keep23) {
		this.keep23 = keep23;
	}
	public String getKeep24() {
		return keep24;
	}
	public void setKeep24(String keep24) {
		this.keep24 = keep24;
	}
	public String getKeep25() {
		return keep25;
	}
	public void setKeep25(String keep25) {
		this.keep25 = keep25;
	}
	public String getKeep26() {
		return keep26;
	}
	public void setKeep26(String keep26) {
		this.keep26 = keep26;
	}
	public String getKeep27() {
		return keep27;
	}
	public void setKeep27(String keep27) {
		this.keep27 = keep27;
	}
	public String getKeep28() {
		return keep28;
	}
	public void setKeep28(String keep28) {
		this.keep28 = keep28;
	}
	public String getKeep29() {
		return keep29;
	}
	public void setKeep29(String keep29) {
		this.keep29 = keep29;
	}
	public String getKeep30() {
		return keep30;
	}
	public void setKeep30(String keep30) {
		this.keep30 = keep30;
	}
	public String getParam1() {
		return param1;
	}
	public void setParam1(String param1) {
		this.param1 = param1;
	}
	public String getParam2() {
		return param2;
	}
	public void setParam2(String param2) {
		this.param2 = param2;
	}
	
}
