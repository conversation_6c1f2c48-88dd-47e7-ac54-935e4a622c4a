package com.wbgame.pojo.jettison.report.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 投放报表接口查询对象
 * @Create 2022-08-10
 */

@ApiModel(value = "投放报表接口查询对象")
public class SpendReportParam extends BaseReportParam {

    @ApiModelProperty(value = "账号归属")
    private List<Integer> type;

    @ApiModelProperty(value = "开始时间（小时）")
    private String start_hour;

    @ApiModelProperty(value = "结束时间（小时）")
    private String end_hour;

    @ApiModelProperty(value = "一级代理商")
    private List<String> first_agent;

    @ApiModelProperty(value = "二级代理商")
    private List<String> agent;

    @ApiModelProperty(value = "投放人员")
    private List<String> putUser;

    @ApiModelProperty(value = "美术人员")
    private List<String> artist;

    @ApiModelProperty(value = "计划名称")
    private String campaignName;

    @ApiModelProperty(value = "广告组名称")
    private String groupName;

    @ApiModelProperty(value = "指标筛选")
    private String key;

    @ApiModelProperty(value = "投放类型")
    private List<String> adsenseType;

    @ApiModelProperty(value = "投放位置")
    private List<String> adsensePosition;

    @ApiModelProperty(value = "开发者主体")
    private List<String> company;

    @ApiModelProperty(value = "渠道产品名称")
    private List<String> gameName;

    @ApiModelProperty(value = "手机平台")
    private List<String> phonePlatform;

    @ApiModelProperty(value = "出价策略")
    private List<String> transferType;

    @ApiModelProperty(value = "是否为最大转化")
    private Integer bidType;

    @ApiModelProperty(value = "对应的大小判断内容")
    private String index;

    @ApiModelProperty(value = "大小判断符号")
    private String symbol;

    @ApiModelProperty(value = "判断数字")
    private Double number;

    @ApiModelProperty(value = "产品系统类型")
    private String os_type;

    @ApiModelProperty(value = "投放模式")
    private String delivery_mode;

    @ApiModelProperty(value = "创意形式")
    private String creative_template;

    @ApiModelProperty(value = "创意形式")
    private List<String> custom_date;

    @ApiModelProperty(value = "包名")
    private List<String> package_name;

    @ApiModelProperty(value = "渠道id")
    private List<String> shop_id;
    
    @ApiModelProperty(value = "投放账号主体")
    private String accountSubject;

    @ApiModelProperty(value = "版位商务类型")
    private Integer bus_type;

    @ApiModelProperty(value = "版位商务类型")
    private Integer app_type;

    @ApiModelProperty(value = "广告组id")
    private String groupId;

    @ApiModelProperty(value = "计划id")
    private String campaignId;

    @ApiModelProperty(value = "账户分组")
    private List<String> account_group;

    @ApiModelProperty(value = "产品自定义分组")
    private String appid_tag;
    @ApiModelProperty(value = "产品自定义分组(反选按钮)")
    private String appid_tag_rev;
    @ApiModelProperty(value = "商店、非商店")
    private String isCPD;
    @ApiModelProperty(value = "oppo第一层级id")
    private String planId;
    @ApiModelProperty(value = "oppo第一层级名")
    private String planName;

    @ApiModelProperty(value = "保障状态")
    private String compensation_status;
    @ApiModelProperty(value = "保障结束时间")
    private String compensation_end_time;
    @ApiModelProperty(value = "保障开始时间")
    private String compensation_begin_time;
    @ApiModelProperty(value = "保障周期")
    private String compensation_period;

    public String getCompensation_status() {
        return compensation_status;
    }

    public void setCompensation_status(String compensation_status) {
        this.compensation_status = compensation_status;
    }

    public String getCompensation_end_time() {
        return compensation_end_time;
    }

    public void setCompensation_end_time(String compensation_end_time) {
        this.compensation_end_time = compensation_end_time;
    }

    public String getCompensation_begin_time() {
        return compensation_begin_time;
    }

    public void setCompensation_begin_time(String compensation_begin_time) {
        this.compensation_begin_time = compensation_begin_time;
    }

    public String getCompensation_period() {
        return compensation_period;
    }

    public void setCompensation_period(String compensation_period) {
        this.compensation_period = compensation_period;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getIsCPD() {
        return isCPD;
    }

    public void setIsCPD(String isCPD) {
        this.isCPD = isCPD;
    }

    public String getAccountSubject() {
		return accountSubject;
	}

	public void setAccountSubject(String accountSubject) {
		this.accountSubject = accountSubject;
	}

	public String getStart_hour() {
        return start_hour;
    }

    public void setStart_hour(String start_hour) {
        this.start_hour = start_hour;
    }

    public String getEnd_hour() {
        return end_hour;
    }

    public void setEnd_hour(String end_hour) {
        this.end_hour = end_hour;
    }

    public List<Integer> getType() {
        return type;
    }

    public void setType(List<Integer> type) {
        this.type = type;
    }

    public List<String> getFirst_agent() {
        return first_agent;
    }

    public void setFirst_agent(List<String> first_agent) {
        this.first_agent = first_agent;
    }

    public List<String> getAgent() {
        return agent;
    }

    public void setAgent(List<String> agent) {
        this.agent = agent;
    }

    public List<String> getPutUser() {
        return putUser;
    }

    public void setPutUser(List<String> putUser) {
        this.putUser = putUser;
    }

    public List<String> getArtist() {
        return artist;
    }

    public void setArtist(List<String> artist) {
        this.artist = artist;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<String> getAdsenseType() {
        return adsenseType;
    }

    public void setAdsenseType(List<String> adsenseType) {
        this.adsenseType = adsenseType;
    }

    public List<String> getAdsensePosition() {
        return adsensePosition;
    }

    public void setAdsensePosition(List<String> adsensePosition) {
        this.adsensePosition = adsensePosition;
    }

    public List<String> getCompany() {
        return company;
    }

    public void setCompany(List<String> company) {
        this.company = company;
    }

    public List<String> getGameName() {
        return gameName;
    }

    public void setGameName(List<String> gameName) {
        this.gameName = gameName;
    }

    public List<String> getPhonePlatform() {
        return phonePlatform;
    }

    public void setPhonePlatform(List<String> phonePlatform) {
        this.phonePlatform = phonePlatform;
    }

    public List<String> getTransferType() {
        return transferType;
    }

    public void setTransferType(List<String> transferType) {
        this.transferType = transferType;
    }

    public Integer getBidType() {
        return bidType;
    }

    public void setBidType(Integer bidType) {
        this.bidType = bidType;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public Double getNumber() {
        return number;
    }

    public void setNumber(Double number) {
        this.number = number;
    }

    public String getOs_type() {
        return os_type;
    }

    public void setOs_type(String os_type) {
        this.os_type = os_type;
    }

    public String getDelivery_mode() {
        return delivery_mode;
    }

    public void setDelivery_mode(String delivery_mode) {
        this.delivery_mode = delivery_mode;
    }

    public String getCreative_template() {
        return creative_template;
    }

    public void setCreative_template(String creative_template) {
        this.creative_template = creative_template;
    }

    public List<String> getCustom_date() {
        return custom_date;
    }

    public void setCustom_date(List<String> custom_date) {
        this.custom_date = custom_date;
    }

    public List<String> getPackage_name() {
        return package_name;
    }

    public void setPackage_name(List<String> package_name) {
        this.package_name = package_name;
    }

    public List<String> getShop_id() {
        return shop_id;
    }

    public void setShop_id(List<String> shop_id) {
        this.shop_id = shop_id;
    }

    public Integer getBus_type() {
        return bus_type;
    }

    public void setBus_type(Integer bus_type) {
        this.bus_type = bus_type;
    }

    public Integer getApp_type() {
        return app_type;
    }

    public void setApp_type(Integer app_type) {
        this.app_type = app_type;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(String campaignId) {
        this.campaignId = campaignId;
    }

    public String getAppid_tag() {
        return appid_tag;
    }

    public void setAppid_tag(String appid_tag) {
        this.appid_tag = appid_tag;
    }

    public String getAppid_tag_rev() {
        return appid_tag_rev;
    }

    public void setAppid_tag_rev(String appid_tag_rev) {
        this.appid_tag_rev = appid_tag_rev;
    }

    public List<String> getAccount_group() {
        return account_group;
    }

    public void setAccount_group(List<String> account_group) {
        this.account_group = account_group;
    }
}

