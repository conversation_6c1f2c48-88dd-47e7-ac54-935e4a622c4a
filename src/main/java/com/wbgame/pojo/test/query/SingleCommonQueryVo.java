package com.wbgame.pojo.test.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Classname MailQueryVo
 * @Description TODO
 * @Date 2022/7/29 12:11
 */
@ApiModel(value = "单独服务查询参数")
public class SingleCommonQueryVo {
    @NotNull(message = "用户id不能为空")
    @ApiModelProperty(value = "用户id", dataType = "String", required = true)
    private String userid;

    @ApiModelProperty(value = "动能设备id", dataType = "String")
    private String lsn;

    @NotNull(message = "应用id不能为空")
    @ApiModelProperty(value = "应用id", dataType = "String", required = true)
    private String appid;

    @ApiModelProperty(value = "项目id", dataType = "String")
    private String pid;

    @ApiModelProperty(value = "androidid安卓设备标识", dataType = "String")
    private String aid;

    @ApiModelProperty(value = "idfa苹果设备标识", dataType = "String")
    private String idfa;

    @NotEmpty(message = "客户端时间戳不能为空")
    @ApiModelProperty(value = "客户端13位时间戳", dataType = "String" ,required = true)
    private String timestamp;

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getLsn() {
        return lsn;
    }

    public void setLsn(String lsn) {
        this.lsn = lsn;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }
}
