package com.wbgame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.adv2.businessAccountEntity.CashHonorAccount;
import com.wbgame.service.adv2.impl.Adv2ServiceImpl;
import com.wbgame.service.impl.CustomServiceImpl;
import com.wbgame.utils.*;
import com.wbgame.utils.customJson.JsonKeyExtractor;
import com.wbgame.utils.monkjay.GenerateSign;
import com.wbgame.utils.monkjay.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;


import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
public class Test {

	public static String accessKey = "1781531433060785358";
	public static String accessSecret = "88ab6d1873973a669218ace2315e8eda3741ed1afff24113c5b227d8f1bf7fae";
	public static String Token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjQzOTU1NjIsInN1YiI6Ik9QUE8tT09QLU9QRU5BUEkiLCJuYmYiOjE3MjQyMjI3NjIsImF1ZCI6Im9wcG8tb29wIiwiaWF0IjoxNzI0MjIyNzYyLCJqdGkiOiJvb3Bfb3BlbmFwaV91c2luZzpkZTdjNGM1MjU0ZWUzZWRmYjNjMGMxMGIwZGNlNjc1OCIsInN0YXR1cyI6MSwiZGF0YSI6eyJjbGllbnRfaWQiOjE3ODE1MzE0MzMwNjA3ODUzNTh9fQ.lrBFIuojBuJ8dRN65jzXCk-_IkKx5Jix-NZ1qDohuAA";

	public static final String tokenUrl = "https://iam.developer.hihonor.com/auth/token";
	public static final String adsUrl = "https://ads.cloud.honor.com/openapi/v2_1/promotion/ad-campaign/page";

	private final static String AES_KEY="x3aesaabb1230000";



	public static final String FS_MSG_GROUP = "https://edc.vigame.cn:6115/fs/sendGroupMsg";


	/**
	 * 解析参数列表中的JSON字符串，提取两层级联键
	 *
	 * @param paramsList JSON字符串列表
	 * @return 包含两层级联键的结果对象
	 */
//	public NestedKeysResult parseNestedJsonKeys(List<String> paramsList) {
//		return JsonKeyExtractor.extractNestedKeys(paramsList);
//	}
	private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(4, 8, 10, TimeUnit.SECONDS,
			new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());

	/**
	 * 多线程方式调用
	 * @param list
	 * @param i
	 */
	private static void processLine(List<String> list, int i) {
		try {
			String[] split = list.get(i).split(",");
			String s1 = split[0];
			String s2 = split[3];
			String s3 = split[2];
			System.out.println(s1 + "," + s2 + "," + s3+" 执行"+i);

			String url = "https://edc.vigame.cn:6115/adv2/adCodeApp/moduleBatchCreateAdCode";
			Map<String, String> headMap = new HashMap<>();
			headMap.put("Content-Type", "application/json");
			headMap.put("token", "wbtokenam4r1xl99pnep8o6w2j5r0x0");

			String json1 = String.format("{\"token\":\"wbtokenam4r1xl99pnep8o6w2j5r0x0\",\"appid\":\"%s\",\"channel\":\"%s\",\"adExtensionName\":\"63\",\"isSyncAdpos\":true,\"ignoreError\":true,\"groupName\":\"屏三方不卡bid补源(横屏）\",\"voList\":[{\"key\":\"oppo-176\",\"label\":\"oppo-原生插屏lt1*2\",\"posName\":\"原生插屏\",\"open_type\":\"plaque\",\"devCrtType\":19,\"exName\":\"pq\",\"adCodeNum\":2,\"adMultiDevCrtTypes\":\"100,6,7,8,11,47,46\",\"select_type\":1,\"posScene\":4,\"configRate\":\"20\",\"platform\":\"oppo\",\"renderMode\":3,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:52\",\"ecpm\":\"1\",\"createTime\":\"2025-05-27 11:58:31\",\"sdk_ad_type\":\"msg\",\"createUser\":\"qinguozhen\",\"id\":176,\"module_name\":\"原生插屏lt1*2\",\"strategy\":\"plaque_rate_new-lt1\"},{\"key\":\"oppo-175\",\"label\":\"oppo-yuans插屏lt1*4\",\"posName\":\"模板插屏\",\"multiDevCrtTypes\":\"30,31\",\"open_type\":\"plaque\",\"devCrtType\":99,\"exName\":\"ypq\",\"adCodeNum\":4,\"select_type\":1,\"posScene\":4,\"configRate\":\"20\",\"platform\":\"oppo\",\"renderMode\":2,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:25\",\"ecpm\":\"4\",\"createTime\":\"2025-05-27 11:56:13\",\"sdk_ad_type\":\"yuans\",\"createUser\":\"qinguozhen\",\"id\":175,\"module_name\":\"yuans插屏lt1*4\",\"strategy\":\"plaque_rate_new-lt1\"},{\"key\":\"oppo-174\",\"label\":\"oppo-yuans msg lt1*4\",\"posName\":\"模板msg\",\"multiDevCrtTypes\":\"30,31\",\"open_type\":\"msg\",\"devCrtType\":99,\"exName\":\"ym\",\"adCodeNum\":4,\"select_type\":1,\"posScene\":4,\"configRate\":\"100\",\"platform\":\"oppo\",\"renderMode\":2,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:44\",\"ecpm\":\"1\",\"createTime\":\"2025-05-27 11:38:07\",\"sdk_ad_type\":\"yuans\",\"createUser\":\"qinguozhen\",\"id\":174,\"module_name\":\"yuans msg lt1*4\",\"strategy\":\"msg_waterfall_xhceshi-lt1\"},{\"key\":\"oppo-112\",\"label\":\"oppo-激励视频*4（横）\",\"posName\":\"激励视频\",\"videoPlayDirection\":1,\"open_type\":\"video\",\"devCrtType\":45,\"multiEndPageStyles\":3,\"adCodeNum\":4,\"select_type\":1,\"posScene\":5,\"configRate\":\"100\",\"platform\":\"oppo\",\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2024-06-04 17:50:20\",\"ecpm\":\"1\",\"createTime\":\"2024-06-04 17:50:20\",\"sdk_ad_type\":\"video\",\"createUser\":\"qinguozhen\",\"id\":112,\"module_name\":\"激励视频*4（横）\",\"strategy\":\"video_waterfall\",\"openVoiceStyle\":1},{\"key\":\"oppo-76\",\"label\":\"oppo-原生msg lt1*4\",\"posName\":\"msg\",\"open_type\":\"msg\",\"devCrtType\":19,\"adCodeNum\":4,\"adMultiDevCrtTypes\":\"100,6,7,8,11,47,46\",\"select_type\":1,\"posScene\":4,\"configRate\":\"100\",\"params\":\"{\\\"isShiled\\\":1,\\\"isShiledApi\\\":1}\",\"platform\":\"oppo\",\"renderMode\":3,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 11:59:53\",\"ecpm\":\"1\",\"createTime\":\"2024-02-08 17:33:41\",\"sdk_ad_type\":\"msg\",\"createUser\":\"qinguozhen\",\"id\":76,\"module_name\":\"原生msg lt1*4\",\"strategy\":\"msg_waterfall_xhceshi-lt1\"}],\"appName\":\"%s\",\"indexCode\":\"YWR2QXBpVGVtcGxhdGVNYW5hZ2U=\",\"__index\":\"advApiTemplateManage\"}"
					,s1,s2,s3);
			String json2 = String.format("{\"token\":\"wbtokenam4r1xl99pnep8o6w2j5r0x0\",\"appid\":\"%s\",\"channel\":\"%s\",\"adExtensionName\":\"63\",\"isSyncAdpos\":true,\"ignoreError\":true,\"groupName\":\"屏三方不卡bid补源(竖屏）\",\"voList\":[{\"key\":\"oppo-176\",\"label\":\"oppo-原生插屏lt1*2\",\"posName\":\"原生插屏\",\"open_type\":\"plaque\",\"devCrtType\":19,\"exName\":\"pq\",\"adCodeNum\":2,\"adMultiDevCrtTypes\":\"100,6,7,8,11,47,46\",\"select_type\":1,\"posScene\":4,\"configRate\":\"20\",\"platform\":\"oppo\",\"renderMode\":3,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:52\",\"ecpm\":\"1\",\"createTime\":\"2025-05-27 11:58:31\",\"sdk_ad_type\":\"msg\",\"createUser\":\"qinguozhen\",\"id\":176,\"module_name\":\"原生插屏lt1*2\",\"strategy\":\"plaque_rate_new-lt1\"},{\"key\":\"oppo-175\",\"label\":\"oppo-yuans插屏lt1*4\",\"posName\":\"模板插屏\",\"multiDevCrtTypes\":\"30,31\",\"open_type\":\"plaque\",\"devCrtType\":99,\"exName\":\"ypq\",\"adCodeNum\":4,\"select_type\":1,\"posScene\":4,\"configRate\":\"20\",\"platform\":\"oppo\",\"renderMode\":2,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:25\",\"ecpm\":\"4\",\"createTime\":\"2025-05-27 11:56:13\",\"sdk_ad_type\":\"yuans\",\"createUser\":\"qinguozhen\",\"id\":175,\"module_name\":\"yuans插屏lt1*4\",\"strategy\":\"plaque_rate_new-lt1\"},{\"key\":\"oppo-174\",\"label\":\"oppo-yuans msg lt1*4\",\"posName\":\"模板msg\",\"multiDevCrtTypes\":\"30,31\",\"open_type\":\"msg\",\"devCrtType\":99,\"exName\":\"ym\",\"adCodeNum\":4,\"select_type\":1,\"posScene\":4,\"configRate\":\"100\",\"platform\":\"oppo\",\"renderMode\":2,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:44\",\"ecpm\":\"1\",\"createTime\":\"2025-05-27 11:38:07\",\"sdk_ad_type\":\"yuans\",\"createUser\":\"qinguozhen\",\"id\":174,\"module_name\":\"yuans msg lt1*4\",\"strategy\":\"msg_waterfall_xhceshi-lt1\"},{\"key\":\"oppo-108\",\"label\":\"oppo-激励视频*4（竖）\",\"posName\":\"激励视频\",\"videoPlayDirection\":2,\"open_type\":\"video\",\"devCrtType\":45,\"multiEndPageStyles\":3,\"adCodeNum\":4,\"select_type\":1,\"posScene\":5,\"configRate\":\"100\",\"platform\":\"oppo\",\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2024-06-04 17:50:00\",\"ecpm\":\"1\",\"createTime\":\"2024-05-15 17:48:39\",\"sdk_ad_type\":\"video\",\"createUser\":\"qinguozhen\",\"id\":108,\"module_name\":\"激励视频*4（竖）\",\"strategy\":\"video_waterfall\",\"openVoiceStyle\":1},{\"key\":\"oppo-76\",\"label\":\"oppo-原生msg lt1*4\",\"posName\":\"msg\",\"open_type\":\"msg\",\"devCrtType\":19,\"adCodeNum\":4,\"adMultiDevCrtTypes\":\"100,6,7,8,11,47,46\",\"select_type\":1,\"posScene\":4,\"configRate\":\"100\",\"params\":\"{\\\"isShiled\\\":1,\\\"isShiledApi\\\":1}\",\"platform\":\"oppo\",\"renderMode\":3,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 11:59:53\",\"ecpm\":\"1\",\"createTime\":\"2024-02-08 17:33:41\",\"sdk_ad_type\":\"msg\",\"createUser\":\"qinguozhen\",\"id\":76,\"module_name\":\"原生msg lt1*4\",\"strategy\":\"msg_waterfall_xhceshi-lt1\"}],\"appName\":\"%s\",\"indexCode\":\"YWR2QXBpVGVtcGxhdGVNYW5hZ2U=\",\"__index\":\"advApiTemplateManage\"}"
					,s1,s2,s3);

			System.out.println(s1+"\tjson==" + json2);
			String httpPost = HttpClientUtils.getInstance().httpPost(url, json2, headMap);
			System.out.println(s1+"\t执行结果:" + httpPost);

			Thread.sleep(2000L);
		}catch (Exception e){
			e.printStackTrace();
			System.out.println("执行失败:" + e.getMessage());
		}
	}




	public static void queryCode(Integer userId, Integer roleId, Integer adUnitId, Integer segmentId, Integer experimentGroup) {
		Map<String, String> params = new HashMap<>();
		params.put("ad_unit_id", adUnitId+"");
		if (segmentId != null) {
			params.put("segment_id", segmentId + "");
		}
		if (experimentGroup != null) {
			params.put("experiment_group", experimentGroup + "");
		}
		Map<String, String> paramsMap = Utils.setSign(userId, roleId, params);
		// paramsMap转为key&val的格式
		String paramsStr = paramsMap.entrySet().stream()
				.map(entry -> entry.getKey() + "=" + entry.getValue())
				.collect(Collectors.joining("&"));
		System.out.println("paramsStr："+paramsStr);
		String resp = HttpClientUtils.getInstance().httpGet(Utils.MEDIATION_URL + "waterfall_detail?"+paramsStr);
		System.out.println("ad_unit resp="+resp);
	}


	public static void main(String[] args) throws Exception {

//		queryCode(294224, 294224, 103495696, null,null);
		long epochMilli = LocalDate.now().minusDays(0).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
		System.out.println(epochMilli);


//		List<String> list = Files.readAllLines(Paths.get("d:/cc2.txt"));
//		System.out.println(JSON.toJSONString(list));
//
//		// main方法中这样调用
//		for (int i = 0; i < list.size(); i++) {
//			int finalI = i;
//			threadPoolExecutor.execute(() -> processLine(list, finalI));
//		}


//		for (int i = 0; i < list.size(); i++) {
//			try {
//				String[] split = list.get(i).split(",");
//				String s1 = split[0];
//				String s2 = split[3];
//				String s3 = split[2];
//				System.out.println(s1 + "," + s2 + "," + s3+" 执行");
//
//				String url = "https://edc.vigame.cn:6115/adv2/adCodeApp/moduleBatchCreateAdCode";
//				Map<String, String> headMap = new HashMap<>();
//				headMap.put("Content-Type", "application/json");
//				headMap.put("token", "wbtokenam4r1xl99pnep8o6w2j5r0x0");
//
//				String json1 = String.format("{\"token\":\"wbtokenam4r1xl99pnep8o6w2j5r0x0\",\"appid\":\"%s\",\"channel\":\"%s\",\"adExtensionName\":\"63\",\"isSyncAdpos\":true,\"ignoreError\":true,\"groupName\":\"屏三方不卡bid补源(横屏）\",\"voList\":[{\"key\":\"oppo-176\",\"label\":\"oppo-原生插屏lt1*2\",\"posName\":\"原生插屏\",\"open_type\":\"plaque\",\"devCrtType\":19,\"exName\":\"pq\",\"adCodeNum\":2,\"adMultiDevCrtTypes\":\"100,6,7,8,11,47,46\",\"select_type\":1,\"posScene\":4,\"configRate\":\"20\",\"platform\":\"oppo\",\"renderMode\":3,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:52\",\"ecpm\":\"1\",\"createTime\":\"2025-05-27 11:58:31\",\"sdk_ad_type\":\"msg\",\"createUser\":\"qinguozhen\",\"id\":176,\"module_name\":\"原生插屏lt1*2\",\"strategy\":\"plaque_rate_new-lt1\"},{\"key\":\"oppo-175\",\"label\":\"oppo-yuans插屏lt1*4\",\"posName\":\"模板插屏\",\"multiDevCrtTypes\":\"30,31\",\"open_type\":\"plaque\",\"devCrtType\":99,\"exName\":\"ypq\",\"adCodeNum\":4,\"select_type\":1,\"posScene\":4,\"configRate\":\"20\",\"platform\":\"oppo\",\"renderMode\":2,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:25\",\"ecpm\":\"4\",\"createTime\":\"2025-05-27 11:56:13\",\"sdk_ad_type\":\"yuans\",\"createUser\":\"qinguozhen\",\"id\":175,\"module_name\":\"yuans插屏lt1*4\",\"strategy\":\"plaque_rate_new-lt1\"},{\"key\":\"oppo-174\",\"label\":\"oppo-yuans msg lt1*4\",\"posName\":\"模板msg\",\"multiDevCrtTypes\":\"30,31\",\"open_type\":\"msg\",\"devCrtType\":99,\"exName\":\"ym\",\"adCodeNum\":4,\"select_type\":1,\"posScene\":4,\"configRate\":\"100\",\"platform\":\"oppo\",\"renderMode\":2,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:44\",\"ecpm\":\"1\",\"createTime\":\"2025-05-27 11:38:07\",\"sdk_ad_type\":\"yuans\",\"createUser\":\"qinguozhen\",\"id\":174,\"module_name\":\"yuans msg lt1*4\",\"strategy\":\"msg_waterfall_xhceshi-lt1\"},{\"key\":\"oppo-112\",\"label\":\"oppo-激励视频*4（横）\",\"posName\":\"激励视频\",\"videoPlayDirection\":1,\"open_type\":\"video\",\"devCrtType\":45,\"multiEndPageStyles\":3,\"adCodeNum\":4,\"select_type\":1,\"posScene\":5,\"configRate\":\"100\",\"platform\":\"oppo\",\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2024-06-04 17:50:20\",\"ecpm\":\"1\",\"createTime\":\"2024-06-04 17:50:20\",\"sdk_ad_type\":\"video\",\"createUser\":\"qinguozhen\",\"id\":112,\"module_name\":\"激励视频*4（横）\",\"strategy\":\"video_waterfall\",\"openVoiceStyle\":1},{\"key\":\"oppo-76\",\"label\":\"oppo-原生msg lt1*4\",\"posName\":\"msg\",\"open_type\":\"msg\",\"devCrtType\":19,\"adCodeNum\":4,\"adMultiDevCrtTypes\":\"100,6,7,8,11,47,46\",\"select_type\":1,\"posScene\":4,\"configRate\":\"100\",\"params\":\"{\\\"isShiled\\\":1,\\\"isShiledApi\\\":1}\",\"platform\":\"oppo\",\"renderMode\":3,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 11:59:53\",\"ecpm\":\"1\",\"createTime\":\"2024-02-08 17:33:41\",\"sdk_ad_type\":\"msg\",\"createUser\":\"qinguozhen\",\"id\":76,\"module_name\":\"原生msg lt1*4\",\"strategy\":\"msg_waterfall_xhceshi-lt1\"}],\"appName\":\"%s\",\"indexCode\":\"YWR2QXBpVGVtcGxhdGVNYW5hZ2U=\",\"__index\":\"advApiTemplateManage\"}"
//					,s1,s2,s3);
//				String json2 = String.format("{\"token\":\"wbtokenam4r1xl99pnep8o6w2j5r0x0\",\"appid\":\"%s\",\"channel\":\"%s\",\"adExtensionName\":\"63\",\"isSyncAdpos\":true,\"ignoreError\":true,\"groupName\":\"屏三方不卡bid补源(竖屏）\",\"voList\":[{\"key\":\"oppo-176\",\"label\":\"oppo-原生插屏lt1*2\",\"posName\":\"原生插屏\",\"open_type\":\"plaque\",\"devCrtType\":19,\"exName\":\"pq\",\"adCodeNum\":2,\"adMultiDevCrtTypes\":\"100,6,7,8,11,47,46\",\"select_type\":1,\"posScene\":4,\"configRate\":\"20\",\"platform\":\"oppo\",\"renderMode\":3,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:52\",\"ecpm\":\"1\",\"createTime\":\"2025-05-27 11:58:31\",\"sdk_ad_type\":\"msg\",\"createUser\":\"qinguozhen\",\"id\":176,\"module_name\":\"原生插屏lt1*2\",\"strategy\":\"plaque_rate_new-lt1\"},{\"key\":\"oppo-175\",\"label\":\"oppo-yuans插屏lt1*4\",\"posName\":\"模板插屏\",\"multiDevCrtTypes\":\"30,31\",\"open_type\":\"plaque\",\"devCrtType\":99,\"exName\":\"ypq\",\"adCodeNum\":4,\"select_type\":1,\"posScene\":4,\"configRate\":\"20\",\"platform\":\"oppo\",\"renderMode\":2,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:25\",\"ecpm\":\"4\",\"createTime\":\"2025-05-27 11:56:13\",\"sdk_ad_type\":\"yuans\",\"createUser\":\"qinguozhen\",\"id\":175,\"module_name\":\"yuans插屏lt1*4\",\"strategy\":\"plaque_rate_new-lt1\"},{\"key\":\"oppo-174\",\"label\":\"oppo-yuans msg lt1*4\",\"posName\":\"模板msg\",\"multiDevCrtTypes\":\"30,31\",\"open_type\":\"msg\",\"devCrtType\":99,\"exName\":\"ym\",\"adCodeNum\":4,\"select_type\":1,\"posScene\":4,\"configRate\":\"100\",\"platform\":\"oppo\",\"renderMode\":2,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 12:23:44\",\"ecpm\":\"1\",\"createTime\":\"2025-05-27 11:38:07\",\"sdk_ad_type\":\"yuans\",\"createUser\":\"qinguozhen\",\"id\":174,\"module_name\":\"yuans msg lt1*4\",\"strategy\":\"msg_waterfall_xhceshi-lt1\"},{\"key\":\"oppo-108\",\"label\":\"oppo-激励视频*4（竖）\",\"posName\":\"激励视频\",\"videoPlayDirection\":2,\"open_type\":\"video\",\"devCrtType\":45,\"multiEndPageStyles\":3,\"adCodeNum\":4,\"select_type\":1,\"posScene\":5,\"configRate\":\"100\",\"platform\":\"oppo\",\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2024-06-04 17:50:00\",\"ecpm\":\"1\",\"createTime\":\"2024-05-15 17:48:39\",\"sdk_ad_type\":\"video\",\"createUser\":\"qinguozhen\",\"id\":108,\"module_name\":\"激励视频*4（竖）\",\"strategy\":\"video_waterfall\",\"openVoiceStyle\":1},{\"key\":\"oppo-76\",\"label\":\"oppo-原生msg lt1*4\",\"posName\":\"msg\",\"open_type\":\"msg\",\"devCrtType\":19,\"adCodeNum\":4,\"adMultiDevCrtTypes\":\"100,6,7,8,11,47,46\",\"select_type\":1,\"posScene\":4,\"configRate\":\"100\",\"params\":\"{\\\"isShiled\\\":1,\\\"isShiledApi\\\":1}\",\"platform\":\"oppo\",\"renderMode\":3,\"modifyUser\":\"qinguozhen\",\"modifyTime\":\"2025-05-27 11:59:53\",\"ecpm\":\"1\",\"createTime\":\"2024-02-08 17:33:41\",\"sdk_ad_type\":\"msg\",\"createUser\":\"qinguozhen\",\"id\":76,\"module_name\":\"原生msg lt1*4\",\"strategy\":\"msg_waterfall_xhceshi-lt1\"}],\"appName\":\"%s\",\"indexCode\":\"YWR2QXBpVGVtcGxhdGVNYW5hZ2U=\",\"__index\":\"advApiTemplateManage\"}"
//					,s1,s2,s3);
//
//				System.out.println(s1+"\tjson==" + json1);
//				String httpPost = HttpClientUtils.getInstance().httpPost(url, json1, headMap);
//				System.out.println(s1+"\t执行结果:" + httpPost);
//
//				Thread.sleep(2000L);
//			}catch (Exception e){
//				e.printStackTrace();
//				System.out.println("执行失败:" + e.getMessage());
//				break;
//			}
//		}
		if(true){return;}



//		String appDetail = getAppDetail("104452099", access_token);
//		System.out.println(appDetail);
//		uploadFilePublish("104452099", access_token, "D:\\40104035_com.dz.emkp.honor_1.0.1_honor_20240808_101_jiagu.apk");


//		header.put("Content-Type", "multipart/form-data");
//		Path path = Paths.get("D:\\40104035_com.dz.emkp.honor_1.0.1_honor_20240808_101_jiagu.apk");
//		String fileName = path.getFileName().toString();
//		String serverUrl = "https://appmarket-openapi-drcn.cloud.honor.com/openapi/v1/publish/upload-file";
//
//		String response = HttpClientUtils.getInstance().uploadFileImpl(serverUrl, path.toFile(), fileName, new HashMap<>(), header);
//		System.out.println("response==" + response);


		/* 更新应用文件信息 */
//		header.put("Content-Type", "application/json");
//		String updateUrl = String.format("https://appmarket-openapi-drcn.cloud.honor.com/openapi/v1/publish/update-file-info?appId=%s","104452099");
//		JSONObject object = new JSONObject();
//		object.put("objectId", "1836975742978756609");
//		String strData = String.format("{\"bindingFileList\": [%s]}",object.toJSONString());
//
//		System.out.println("strData==" + strData);
//		String response2 = HttpClientUtils.getInstance().httpPost(updateUrl, strData, header, true);
//		System.out.println("post response2==" + response2);
//		if(!BlankUtils.isJSONObject(response2) || !"0".equals(JSONObject.parseObject(response2).getString("code"))) {
//			System.out.println("更新应用文件信息失败: " + response2);
//		}

		/* 应用提交审核 */
//		String url = String.format("https://appmarket-openapi-drcn.cloud.honor.com/openapi/v1/publish/submit-audit?appId=%s","104452099");
//		// releaseType：1-全网发布 2-指定时间发布，releaseTime：指定时间 yyyy-MM-dd'T'HH:mm:ssZZ
//		String str = String.format("{\"releaseType\": 1}");
//
//		System.out.println("str==" + str);
//		String response3 = HttpClientUtils.getInstance().httpPost(url, str, header, true);
//
//		System.out.println("post response3==" + response3);
//		if(BlankUtils.isJSONObject(response3) && "0".equals(JSONObject.parseObject(response3).getString("code"))) {
//			String releaseId = JSONObject.parseObject(response3).getString("data");
//
//			System.out.println("应用提交审核成功，返回releaseId="+releaseId);
//		}else{
//			System.out.println("应用提交审核失败: " + response3);
//
//		}

//		uploadFilePublish("104457103", access_token, "D:\\39372019_com.yh.pddzz.nearme.gamecenter_1.0.2_oppo_20240815.apk");

		if (true) {
			return;
		}


	}

	/**
	 * Writes data and styles to a Feishu spreadsheet.
	 *
	 * @param token              The Feishu API token.
	 * @param spreadsheetToken   The spreadsheet token.
	 * @param sheetId            The sheet ID.
	 * @param headerList       The list of header parameters in the format "HeaderName,FieldName".
	 * @param dataList           The list of data to be written.
	 */
	public static boolean writeDataToFeishuSpreadsheet(String token, String spreadsheetToken, String sheetId, List<String> headerList, List<JSONObject> dataList) {
		Map<String, String> headerMap = new LinkedHashMap<>();
		JSONArray headDataArray = new JSONArray();
		JSONArray dataArray = new JSONArray();
		JSONArray headStyleArray = new JSONArray();

		// 设置表头的样式配置
		JSONObject headerStyleData = new JSONObject();
		JSONArray headerStyleRangesArray = new JSONArray();
		JSONObject headerStyle = JSONObject.parseObject("{\"font\":{\"bold\":true,\"italic\":false,\"fontSize\":\"10pt/1.5\",\"clean\":false},\"textDecoration\":0,\"formatter\":\"\",\"hAlign\":1,\"vAlign\":1,\"foreColor\":\"#000000\",\"backColor\":\"#33CCCC\",\"borderType\":\"FULL_BORDER\",\"borderColor\":\"#000000\",\"clean\":false}");

		// 解析保存表头数据
		JSONObject headerData = new JSONObject();
		List<String> headersValue = new ArrayList<>();
		try {
			for (String param : headerList) {
				String[] s = param.split(",");
				headersValue.add(s[1]);
				headerMap.put(s[0], s[1]);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		JSONArray headerArray = new JSONArray();
		headerArray.add(headersValue);

		// 写入表头数据
		String headRightRangeLetter = StringUtils.numberToLetter(headersValue.size());
		String headRange = sheetId + "!" + "A1:" + headRightRangeLetter;

		headerData.put("range", headRange);
		headerData.put("values", headerArray);
		headDataArray.add(headerData);
		boolean headInsertSucc = FeishuUtils.batchInsertSpreadsheetData(token, spreadsheetToken, headDataArray);
		System.out.println("headInsertSucc==" + headInsertSucc);

		// 定义表头样式
		headerStyleRangesArray.add(headRange + "1");
		headerStyleData.put("ranges", headerStyleRangesArray);
		headerStyleData.put("style", headerStyle);
		headStyleArray.add(headerStyleData);

		boolean headStyleSucc = FeishuUtils.batchHandleStyle(token, spreadsheetToken, headStyleArray);
		System.out.println("headStyleSucc==" + headStyleSucc);

		// 组装数据行，必须从第二行开始
		JSONObject dataData = new JSONObject();
		JSONArray dataDataArray = FeishuUtils.assembleFeishuExcelData(dataList, headerMap);

		// 每2000条写入一次，防止飞书 Excel 出现 "request data too large" 错误
		if (dataDataArray.size() <= 2000) {
			String dataLetter = StringUtils.numberToLetter(headerMap.size());
			int dataSize = dataList.size() + 1;
			String dataRange = sheetId + "!" + "A2:" + dataLetter + dataSize;
			dataData.put("range", dataRange);
			dataData.put("values", dataDataArray);
			dataArray.add(dataData);
			boolean insertSucc = FeishuUtils.batchInsertSpreadsheetData(token, spreadsheetToken, dataArray);
			System.out.println("insertSucc==" + insertSucc);
			return insertSucc;
		} else {
			int pageSize = 1999;
			int pageNum = (dataDataArray.size() / pageSize) + 1;
			int column = 0;
			for (int i = 0; i < pageNum; i++) {
				int fromIndex = pageSize * i;
				int toIndex = Math.min(fromIndex + pageSize, dataDataArray.size());
				List<?> eachDataArray = dataDataArray.subList(fromIndex, toIndex);
				int row = (i == 0) ? 2 : column + 1;
				column = (i == 0) ? eachDataArray.size() + 1 : column + eachDataArray.size();

				String dataLetter = StringUtils.numberToLetter(headerMap.size());
				String dataRange = sheetId + "!" + "A" + row + ":" + dataLetter + column;
				System.out.println("dataRange:" + dataRange);
				dataData.put("range", dataRange);
				dataData.put("values", eachDataArray);
				dataArray.add(dataData);
				boolean insertSucc = FeishuUtils.batchInsertSpreadsheetData(token, spreadsheetToken, dataArray);
				System.out.println("insertSucc==" + insertSucc);
				return insertSucc;
			}
		}
		return false;
	}



	public static void img2img() throws Exception {

		String url = "https://sd-fc-stabion-plus-titzjbsrtv.cn-shenzhen.fcapp.run";

		String[] init_images = {
				Base64.encodeBase64String(Files.readAllBytes(Paths.get("D:\\tmp\\b825e772357376669f7e103c6ba78481.png")))
		};

		String payload = "{ \"prompt\": \"3girl, blue hair\", \"seed\": 1, \"steps\": 20, \"width\": 512, \"height\": 512, " +
				"	\"denoising_strength\": 0.5, \"n_iter\": 1, \"init_images\": " + JSONArray.toJSONString(init_images) + ", \"batch_size\": 2 }";

		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("Content-Type", "application/json");

		String httpPost = HttpClientUtils.getInstance().httpPost(url + "/sdapi/v1/img2img", payload, headerMap);
		System.out.println("img2img httpPost == " + httpPost);
		if (BlankUtils.isJSONObject(httpPost) && httpPost.contains("images")) {
			JSONObject jsonObject = JSON.parseObject(httpPost);
			JSONArray images = jsonObject.getJSONArray("images");
			for (int i = 0; i < images.size(); i++) {
				String image = images.getString(i);

				byte[] bytePic = Base64.decodeBase64(image);
				String fileMd5Name = DigestUtils.md5Hex(bytePic) + ".png";
				System.out.println(i + "\t" + fileMd5Name);

				Path path = Paths.get("/tmp/" + fileMd5Name);
				Files.write(path, bytePic, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
			}

			jsonObject.remove("images");
			System.out.println("img2img jsonObject==" + jsonObject.toJSONString());
		}

	}

	public static void txt2img() throws Exception {
		String url = "https://sd-fc-stabion-plus-titzjbsrtv.cn-shenzhen.fcapp.run";

		String payload = "{ \"prompt\": \"1 girl, sunshine, dog\",\n" +
				"        \"step\": 10,\n" +
				"        \"height\": 512,\n" +
				"        \"width\": 1024 }";

		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("Content-Type", "application/json");

		String httpPost = HttpClientUtils.getInstance().httpPost(url + "/sdapi/v1/txt2img", payload, headerMap);
		if (BlankUtils.isJSONObject(httpPost) && httpPost.contains("images")) {
			JSONObject jsonObject = JSON.parseObject(httpPost);
			JSONArray images = jsonObject.getJSONArray("images");
			for (int i = 0; i < images.size(); i++) {
				String image = images.getString(i);

				byte[] bytePic = Base64.decodeBase64(image);
				String fileMd5Name = DigestUtils.md5Hex(bytePic) + ".png";
				System.out.println(i + "\t" + fileMd5Name);

				Path path = Paths.get("/tmp/" + fileMd5Name);
				Files.write(path, bytePic, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
			}

			jsonObject.remove("images");
			System.out.println("jsonObject==" + jsonObject.toJSONString());
		}
	}


	/**
	 * 将图片转换为Base64字符串
	 *
	 * @param imageUrl
	 * @return
	 */
	private static String imgToBase64(String imageUrl) {
		String base64Image = "";

		CloseableHttpClient httpclient = HttpClients.createDefault();
		try {
			HttpGet httpGet = new HttpGet(imageUrl);
			CloseableHttpResponse response = httpclient.execute(httpGet);
			try {
				byte[] fileContent = EntityUtils.toByteArray(response.getEntity());
				base64Image = Base64.encodeBase64String(fileContent);
			} finally {
				response.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
//			logger.error("图片转换Base64出错,imageUrl:{},err:{}", imageUrl, e);
		} finally {
			try {
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return base64Image;
	}

	/**
	 * 下载网络文件到指定地址，同步执行的
	 *
	 * @param url      线上url
	 * @param filePath 本地目录+文件名
	 */
	public static void downloadFile(String url, String filePath, boolean isTimeout) {
		CloseableHttpClient httpclient = HttpClients.createDefault();
		try {
			HttpGet httpGet = new HttpGet(url);
			if (isTimeout) {
				RequestConfig requestConfig = RequestConfig.custom()
						.setConnectTimeout(8000)
						.setConnectionRequestTimeout(5000)
						.setSocketTimeout(8000).build();
				httpGet.setConfig(requestConfig);
			}
			CloseableHttpResponse response = httpclient.execute(httpGet);
			try {
				HttpEntity httpEntity = response.getEntity();
				InputStream is = httpEntity.getContent();
				// 根据InputStream 下载文件
				ByteArrayOutputStream output = new ByteArrayOutputStream();
				byte[] buffer = new byte[4096];
				int r = 0;
				while ((r = is.read(buffer)) > 0) {
					output.write(buffer, 0, r);
				}
				FileOutputStream fos = new FileOutputStream(filePath);
				output.writeTo(fos);
				output.flush();
				output.close();
				fos.close();
				EntityUtils.consume(httpEntity);
			} finally {
				response.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

}
