package com.wbgame.servlet;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.utils.ApplicationContextUtils;
import com.wbgame.utils.OSSUploadUtil;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;



/**
 * 文件上传接口
 */

@WebServlet(name="uploadFileServlet",urlPatterns="/uploadFileServlet")
public class UploadFileServlet extends HttpServlet {

	private static final long serialVersionUID = 6240654150052709227L;
	//阿里云上传OSS参数
	private static String endpoint = "https://oss-cn-shenzhen.aliyuncs.com";
	private static String accessKeyId = "LTAImrFvdsZ2E6Vd";
	private static String accessKeySecret = "wWc1X7B8qjWJ1DarzBUyysAT71Dpg2";
	//private static String HOME_PATH = "vmhome";
	private static String HOME_PATH = "dnwx-res";
	private static String PATH = "apk/push";
	public static Logger log = LoggerFactory.getLogger(UploadFileServlet.class);

	public void doGet(HttpServletRequest request, HttpServletResponse response) 
				throws ServletException, IOException {
		
		//跨域返回
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setCharacterEncoding("UTF-8");
		response.setContentType("text/html;charset=utf-8");  
				
		String path = request.getParameter("path");
        if(path == null || path.isEmpty()){
            path = PATH;
        }

        //文件临时路径
//		String savePath = "/home/<USER>";
//		String savePath = "D://";
		Environment environment = ApplicationContextUtils.get(Environment.class);
		String savePath = environment.getProperty("savePath");
//		String savePath = "/home/<USER>/project/ROOT";
		File file = new File(savePath);
		if (!file.exists() && !file.isDirectory()) {
			System.out.println("目录不存在，需要创建");
			file.mkdir();
		}
		String result = "";
		//阿里云上传工具
		//OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
		try{
			DiskFileItemFactory factory = new DiskFileItemFactory();
			//设置内存缓存区大小
			factory.setSizeThreshold(50000);
			ServletFileUpload upload = new ServletFileUpload(factory);
			//设置文件上传最大值
			//upload.setFileSizeMax(1000);
			upload.setHeaderEncoding("UTF-8"); 
			if(!ServletFileUpload.isMultipartContent(request)){
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = response.getWriter();
				out.print("{\"success\":\"false\"}");
				out.close();
				return;
			}
			List<FileItem> list = upload.parseRequest(request);
			for(FileItem item : list){
				if(!item.isFormField()){
					String filename = item.getName();
					InputStream in = item.getInputStream();
					FileOutputStream out = new FileOutputStream(savePath + "/" + filename);
					byte buffer[] = new byte[2048];
					int len = 0;
					while((len = in.read(buffer)) > 0){
						out.write(buffer, 0, len);
					}
					File upFile = new File(savePath + "/" + filename); 
					//图片压缩
					//ossClient.putObject(HOME_PATH, PATH+"/"+filename, upFile);
                    OSSUploadUtil.uploadDataOSS(HOME_PATH, path+"/"+filename, upFile, null);

                    upFile.delete();
					item.delete();
					result = result + "http://a.vigame.cn/"+path+"/"+filename+",";
					in.close();
					out.close();
				}
			}
			result = result.substring(0, result.length()-1);
			
			JSONObject json = new JSONObject();
			json.put("success", "true");
			json.put("url", result);
			json.put("type", "apk");
			
			PrintWriter out = response.getWriter();
			out.write(json.toString());
			out.close();
		}catch (Exception e) {
			//上传异常                	
			e.printStackTrace();
			JSONObject json = new JSONObject();
			json.put("success", "false");
			
			PrintWriter out = response.getWriter();
			out.write(json.toString());
			out.close();
		}
	}

	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doGet(request, response);
	}
	
	@Override
	public void init() throws ServletException {
		super.init();
	}
	@Override
	public void destroy() {
		super.destroy();
	}
	
}