package com.wbgame.controller.jettison.report;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.mysql.jdbc.StringUtils;
import com.wbgame.annotation.NullValueSorter;
import com.wbgame.aop.LoginCheck;
import com.wbgame.aop.RedisLock;
import com.wbgame.common.Asserts;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.common.param.CommonSynParam;
import com.wbgame.pojo.finance.FinanceReport;
import com.wbgame.pojo.jettison.report.*;
import com.wbgame.pojo.jettison.report.dto.MonetizationReportDTO;
import com.wbgame.pojo.jettison.report.param.*;
import com.wbgame.service.WbSysService;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.service.budgetWarning.BaseBudgetService;
import com.wbgame.service.jettison.report.SpendReportService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.DateUtils;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static com.alicp.jetcache.Cache.logger;


@CrossOrigin
@RestController
@RequestMapping("/report")
@Api(tags = "投放数据管理")
@ApiSupport(author = "xujingyu")
@Slf4j
public class ReportController {

    @Autowired
    SpendReportService spendReportService;

    @Autowired
    private Adv2Service adv2Service;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private WbSysService wbSysService;

    @Resource
    private BaseBudgetService vivoBudgetService;
    @Resource
    private BaseBudgetService oppoBudgetService;
    @Resource
    private BaseBudgetService xiaomiBudgetService;
    @Resource
    private ExecutorService spendAPIExecutor;

    @ApiOperation(value = "联运产品数据报表")
    @PostMapping("/operation/list")
    @LoginCheck
    public InfoResult getOperationReport(@RequestBody OperationReportParam param,HttpServletRequest request) {
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        handleReportApps(param,loginUser);
        InfoResult infoResult = spendReportService.getOperationReport(param);
        return infoResult;
    }

    @ApiOperation(value = "联运产品数据报表查询自动飞书报表接口")
    @PostMapping("/operation/common")
    @LoginCheck
    public InfoResult getOperationCommonReport(OperationReportParam param,HttpServletRequest request) {
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        handleReportApps(param,loginUser);
        InfoResult infoResult = spendReportService.getOperationReportNotLimit(param);
        return infoResult;
    }

    @ApiOperation(value = "联运产品数据报表导出")
    @PostMapping(value = "/operation/export")
    @LoginCheck
    public void getOperationReportExport(@RequestBody OperationReportParam param, HttpServletResponse response,HttpServletRequest request) {
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        handleReportApps(param,loginUser);
        spendReportService.getOperationReportExport(param,response);
    }

    @ApiOperation(value = "投放细分数据查询")
    @PostMapping("/spend/list")
    public InfoResult getSpendReport(@RequestBody SpendReportParam spendReportParam) {
        InfoResult infoResult = new InfoResult();
        try {
            String token = spendReportParam.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            //处理app权限问题
            handleReportApps(spendReportParam,currUserVo);
            infoResult = spendReportService.getSpendReport(spendReportParam,username);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getSpendReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "投放细分数据查询自动飞书报表接口")
    @PostMapping("/spend/common")
    @LoginCheck
    public InfoResult getSpendReportCommon(SpendReportParam spendReportParam, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            String username;
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            username = currUserVo.getLogin_name();
            //处理app权限问题
            handleReportApps(spendReportParam,currUserVo);
            infoResult = spendReportService.getSpendReportNotLimit(spendReportParam,username);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getSpendReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "投放细分数据导出")
    @PostMapping(value = "/spend/export", produces = MediaType.TEXT_PLAIN_VALUE)
    public void exportSpendReport(@RequestBody SpendReportParam param,HttpServletResponse response) {
        try {
            String token = param.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                return ;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            //处理app权限问题
            handleReportApps(param,currUserVo);
            spendReportService.exportSpendReport(param, username,response);
        } catch (Exception e) {
            logger.error("exportSpendReport: ", e);
        }
    }

    @ApiOperation(value = "投放小时级别数据查询")
    @PostMapping("/hourSpend/list")
    public InfoResult getHourSpendReport(@RequestBody SpendReportParam spendReportParam) {
        InfoResult infoResult = new InfoResult();
        try {
            String token = spendReportParam.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();

            infoResult = spendReportService.getHourSpendReport(spendReportParam,username);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getSpendReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "投放小时级别数据导出")
    @PostMapping(value = "/hourSpend/export", produces = MediaType.TEXT_PLAIN_VALUE)
    public void exportHourSpendReport(@RequestBody SpendReportParam param,HttpServletResponse response) {
        try {
            String token = param.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                return ;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();

            spendReportService.exportHourSpendReport(param, username,response);
        } catch (Exception e) {
            logger.error("exportSpendReport: ", e);
        }
    }

    @ApiOperation(value = "广告投放汇总报表")
    @PostMapping("/account/spend/list")
    public InfoResult getAccountSpendReport(@RequestBody SpendReportParam spendReportParam) {
        InfoResult infoResult = new InfoResult();
        try {
            String token = spendReportParam.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            handleReportApps(spendReportParam,currUserVo);
            infoResult = spendReportService.getAccountSpendReport(spendReportParam, username);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getChinaAccountSpendReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "广告投放汇总报表自动飞书报表接口")
    @PostMapping("/account/spend/common")
    @LoginCheck
    public InfoResult getAccountSpendReportCommon(SpendReportParam spendReportParam, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            String username;
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            username = currUserVo.getLogin_name();
            handleReportApps(spendReportParam,currUserVo);
            infoResult = spendReportService.getAccountSpendReportNotLimit(spendReportParam, username);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getChinaAccountSpendReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "广告投放汇总报表导出")
    @PostMapping(value = "/account/spend/export")
    public void getAccountSpendReportExport(@RequestBody SpendReportParam spendReportParam, HttpServletResponse response) {
        try {
            // token验证
            String token = spendReportParam.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                return ;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            handleReportApps(spendReportParam,currUserVo);
            spendReportService.getAccountSpendReportExport(spendReportParam,response);

        } catch (Exception e) {
            logger.error("getAccountSpendReportExport: ", e);
        }

    }

    @ApiOperation(value = "渠道账号汇总表")
    @PostMapping("/channel/account/spend/list")
    public InfoResult getChannelAccountSpendReport(@RequestBody SpendReportParam spendReportParam) {
        InfoResult infoResult = new InfoResult();
        try {
            String token = spendReportParam.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            handleReportApps(spendReportParam,currUserVo);
            PageHelper.startPage(spendReportParam.getStart(),spendReportParam.getLimit());
            infoResult = spendReportService.getChannelAccountSpendReport(spendReportParam, username);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getChinaAccountSpendReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "渠道账号汇总表导出")
    @PostMapping(value = "/channel/account/spend/export")
    public void getChannelAccountSpendReportExport(@RequestBody SpendReportParam spendReportParam, HttpServletResponse response) {
        try {
            // token验证
            String token = spendReportParam.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                return ;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
//            handleReportApps(spendReportParam,currUserVo);
            InfoResult result = spendReportService.getChannelAccountSpendReport(spendReportParam, currUserVo.getUser_name());
            Map<String, Object> map = (Map<String, Object>) result.getData();
            List<FinanceReport> list = (List<FinanceReport>) map.get("list");
            String value = spendReportParam.getValue();
            Map<String, String> headerMap = new LinkedHashMap<String, String>();
            if (!StringUtils.isNullOrEmpty(value)){
                //自定义列数据
                try {
                    String[] split = value.split(";");
                    for (int i = 0;i<split.length;i++) {
                        String[] s = split[i].split(",");
                        headerMap.put(s[0], s[1]);
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                    Asserts.fail("自定义列导出异常");
                }
            }
            ExportExcelUtil.exportXLSX2(response, list, headerMap, "渠道账号汇总表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
        } catch (Exception e) {
            logger.error("getAccountSpendReportExport: ", e);
        }

    }

    @ApiOperation(value = "同步渠道账号汇总表")
    @PostMapping("/channel/account/spend/syn")
    @LoginCheck
    @RedisLock(name = "synChannelAccountSpendReport",timeout = 30*60*1000)
    public InfoResult synChannelAccountSpendReport(@RequestBody CommonSynParam commonSynParam) {
        InfoResult infoResult = new InfoResult();
        String ad_platform = commonSynParam.getAd_platform();
        String startDate = commonSynParam.getStartDate();
        String endDate = commonSynParam.getEndDate();
        if (BlankUtils.checkBlank(ad_platform) || BlankUtils.checkBlank(startDate) || BlankUtils.checkBlank(endDate)) {
            infoResult.setRet(0);
            infoResult.setMsg("参数有误");
            return infoResult;
        }
        try {
            long start = System.currentTimeMillis();
            log.info("渠道流水当日拉取开始, 媒体 = {}, 同步日期 = {} ~ {}",ad_platform,startDate,endDate);

            List<String> dateList = DateUtils.getHyphenDateList(startDate, endDate);

            if (dateList.size() > 7) {
                infoResult.setRet(0);
                infoResult.setMsg("同步时间不能超过7天");
                return infoResult;
            }

            for (String date : dateList) {
                log.info("渠道流水当日拉取开始, 媒体={}, 日期 = {}",ad_platform,date);
                switch (ad_platform) {
                    case "oppo" :
                        Future<JSONObject> oppo = spendAPIExecutor.submit(() -> oppoBudgetService.processFinanceData(date));
                        oppo.get();
                        break;
                    case "vivo" :
                        Future<JSONObject> vivo = spendAPIExecutor.submit(() -> vivoBudgetService.processFinanceData(date));
                        vivo.get();
                        break;
                    case "xiaomi" :
                        Future<JSONObject> xiaomi = spendAPIExecutor.submit(() -> xiaomiBudgetService.processFinanceData(date));
                        xiaomi.get();
                        break;
                    default:
                        infoResult.setRet(0);
                        infoResult.setMsg("渠道媒体有误");
                        return infoResult;
                }
            }

            long mid = System.currentTimeMillis();
            log.info("渠道流水拉取结束, 媒体 = {}, 同步日期 = {} ~ {}, 花费时间 = {}", ad_platform,startDate,endDate,(mid - start) / 1000);

        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("同步失败");
            logger.error("synChannelAccountSpendReport: ", e);
        }
        infoResult.setRet(1);
        infoResult.setMsg("同步成功");
        return infoResult;
    }

    @ApiOperation(value = "直投数据报表")
    @PostMapping("/direct/list")
    public InfoResult getDirectReport(@RequestBody DirectReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            String token = param.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            infoResult = spendReportService.getDirectReport(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getDirectReport: ", e);
        }
        return infoResult;
    }


    /**
     * 根据条件不分页查询直投数据报表
     * @param param 查询条件
     * @return 查询结果
     */
    @ApiOperation(value = "不分页直投数据报表查询")
    @PostMapping("/direct/common")
    public InfoResult getDirectReportNotLimit(DirectReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            String token = param.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            infoResult = spendReportService.getDirectReportNotLimit(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getDirectReport: ", e);
        }
        return infoResult;
    }



    @ApiOperation(value = "直投数据报表导出")
    @PostMapping(value = "/direct/export")
    public void getDirectReportExport(@RequestBody DirectReportParam param, HttpServletResponse response) {
        try {
            // token验证
            String token = param.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                return ;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            spendReportService.getDirectReportExport(param,response);

        } catch (Exception e) {
            logger.error("getDirectReportExport: ", e);
        }
    }

    @ApiOperation(value = "订阅ROI报表")
    @PostMapping("/subscribe/list")
    @LoginCheck
    public InfoResult getSubscribeRoiReport(@RequestBody SubscribeRoiReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = spendReportService.getSubscribeRoiReport(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getSubscribeRoiReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "订阅ROI报表导出")
    @PostMapping(value = "/subscribe/export")
    @LoginCheck
    public void getSubscribeRoiReportExport(@RequestBody SubscribeRoiReportParam param, HttpServletResponse response) {
        try {
            spendReportService.getSubscribeRoiReportExport(param,response);
        } catch (Exception e) {
            logger.error("getSubscribeRoiReportExport: ", e);
        }
    }

    @ApiOperation(value = "新增对比报表")
    @PostMapping("/adduser/list")
    @LoginCheck
    public InfoResult getAddUserReport(@RequestBody AddUserReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = spendReportService.getAddUserReport(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getAddUserReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "新增对比报表导出")
    @PostMapping(value = "/adduser/export")
    @LoginCheck
    public void exportAddUserReport(@RequestBody AddUserReportParam param, HttpServletResponse response) {
        try {
            spendReportService.exportAddUserReport(param,response);
        } catch (Exception e) {
            logger.error("exportAddUserReport: ", e);
        }
    }

    @ApiOperation(value = "变现汇总表")
    @PostMapping("/monetization/list")
    @LoginCheck
    @NullValueSorter
    public InfoResult getMonetizationReport(MonetizationReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = spendReportService.getMonetizationReport(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getMonetizationReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "变现汇总表导出")
    @PostMapping(value = "/monetization/export")
    @LoginCheck
    @NullValueSorter
    public void exportMonetizationReport(MonetizationReportParam param, HttpServletResponse response) {
        try {
            spendReportService.exportMonetizationReport(param,response);
        } catch (Exception e) {
            logger.error("exportMonetizationReport: ", e);
        }
    }

    @ApiOperation(value = "变现汇总表同步")
    @PostMapping(value = "/monetization/sync")
    @LoginCheck
    public InfoResult syncMonetizationReport(MonetizationReportParam param, HttpServletResponse response) {
        InfoResult infoResult = new InfoResult();

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("start_date", param.getStart_date());
        paramMap.put("end_date", param.getEnd_date());
        try {
            boolean resp = adv2Service.syncMonetizationReport(paramMap);
            if(resp){
                infoResult.setRet(1);
                infoResult.setMsg("同步成功");
            }else{
                infoResult.setRet(0);
                infoResult.setMsg("同步失败，请稍后重试！");
            }
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("同步失败，请联系管理员排查！");
            logger.error("getMonetizationReport: ", e);
        }
        return infoResult;
    }


    /**
     * 获取变现汇总表数据-通用接口
     * @param param
     * @return
     */
    @RequestMapping("/monetization/common")
    @LoginCheck
    public Object common(MonetizationReportParam param){
        List<MonetizationReportDTO> list = spendReportService.commonMonetizationReport(param);
        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        return result;
    }

    /**
     * 处理国内报表app权限
     * @param param
     */
    public <T extends BaseReportParam>void handleReportApps(T param,CurrUserVo currUserVo){
        //判断是否传app
        if (param.getApp() != null && param.getApp().size() > 0) {
            return;
        }
        //判断是飞书发送还是查询导出
        String app_group = "";
        String category_group = "";
        if (!BlankUtils.checkBlank(param.getFs_user())) {
            JSONObject jsonObject = wbSysService.selectUserRoleForName(param.getFs_user());
            app_group = jsonObject.getString("appid");
            category_group = jsonObject.getString("app_category");
        }else{
            app_group = currUserVo.getApp_group();
            category_group = currUserVo.getApp_category();
        }
        //判断是否app权限
        if (!BlankUtils.checkBlank(app_group)) {
            try {
                String[] split = app_group.split(",");
                param.setApp(Arrays.asList(split));
            }catch (Exception e) {
                logger.error("国内投放报表:获取应用权限失败",e);
            }
            return;
        }
        //判断是否传应用分类
        if (param.getAppCategory() == null || param.getAppCategory().size() == 0) {
            //判断是否有分类权限
            if (!BlankUtils.checkBlank(category_group)) {
                try {
                    String[] split = category_group.split(",");
                    param.setAppCategory(Arrays.asList(split));
                }catch (Exception e) {
                    logger.error("国内投放报表:获取应用分类权限失败",e);
                }
            }
        }
    }
}
