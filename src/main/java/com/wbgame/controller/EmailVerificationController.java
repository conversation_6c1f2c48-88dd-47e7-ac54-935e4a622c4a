package com.wbgame.controller;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.EmailVerificationCodeDTO;
import com.wbgame.pojo.EmailVerificationCodeVo;
import com.wbgame.service.EmailVerificationCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 邮件验证码管理控制器
 * @Date 2025/01/16
 */
@CrossOrigin
@RestController
@RequestMapping("/adv2/verification")
public class EmailVerificationController {

    @Autowired
    private EmailVerificationCodeService emailVerificationCodeService;

    /**
     * 拉取邮件并解析验证码
     *
     * @param email 验证邮箱
     * @return 处理结果
     */
    @PostMapping("/fetch")
    public String fetchEmailsAndParseCode(@RequestParam(value = "accountInfo", required = false) String accountInfo) {

        emailVerificationCodeService.fetchEmailsAndParseCode(accountInfo);
        return ReturnJson.success("操作成功！");
    }

    /**
     * 根据邮箱查询验证码记录
     *
     * @param dto 查询参数
     * @return 验证码记录列表
     */
    @GetMapping("/query")
    public String queryVerificationCodes(EmailVerificationCodeDTO dto) {

        EmailVerificationCodeVo codeVo = emailVerificationCodeService.queryVerificationCodes(dto);
        if (codeVo != null) {
            return ReturnJson.success(codeVo);
        }else{
            return ReturnJson.toErrorJson("未找到匹配的验证码记录");
        }
    }
}
