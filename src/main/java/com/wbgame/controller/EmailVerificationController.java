package com.wbgame.controller;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.EmailVerificationCodeDTO;
import com.wbgame.pojo.EmailVerificationCodeVo;
import com.wbgame.service.EmailVerificationCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 邮件验证码管理控制器
 * @Date 2025/01/16
 */
@CrossOrigin
@RestController
@RequestMapping("/email/verification")
public class EmailVerificationController {

    @Autowired
    private EmailVerificationCodeService emailVerificationCodeService;

    /**
     * 拉取邮件并解析验证码
     *
     * @param email 验证邮箱
     * @return 处理结果
     */
    @ControllerLoggingEnhancer
    @PostMapping("/fetch")
    public String fetchEmailsAndParseCode(@RequestParam String email) {
        if (StringUtils.isEmpty(email)) {
            return ReturnJson.toErrorJson("邮箱地址不能为空");
        }
        emailVerificationCodeService.fetchEmailsAndParseCode(email);
        return ReturnJson.success("操作成功！");
    }

    /**
     * 根据邮箱查询验证码记录
     *
     * @param dto 查询参数
     * @return 验证码记录列表
     */
    @ControllerLoggingEnhancer
    @GetMapping("/query")
    public String queryVerificationCodes(EmailVerificationCodeDTO dto) {

        return ReturnJson.success(emailVerificationCodeService.queryVerificationCodes(dto));
    }
}
