package com.wbgame.controller.redpack;

import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.slave2.RedPackMapper;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 红包特殊处理
 * @author: huangmb
 * @date: 2021/05/28
 **/
@CrossOrigin
@RestController
@RequestMapping(value = "/redpack/handle")
public class RedPackHandleController {

    @Autowired
    private RedPackMapper redPackMapper;

    /***
     * 记录日期范围内的用户数据(抽取用户登录日志)
     * @param start
     * @param end
     * @param appid
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/recordUserData", method = {RequestMethod.GET, RequestMethod.POST})
    public String recoreUser(String start,String end,String appid) {
        //转换日期
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
        DateTime s = DateTime.parse(start, format);
        DateTime e = DateTime.parse(end, format);
        Map<String,Object> param = new HashMap<>();
        param.put("appid",appid);
        //循环插入日志
        while (s.compareTo(e) <= 0) {
            param.put("ds",s.toString("yyyyMMdd"));
            List<Map<String, Object>> list = redPackMapper.selectHomeDiamondLog(param);
            if (list != null && list.size() != 0) {
                redPackMapper.batchinsertUserRecord(list);
            }
            s = s.plusDays(1);
        }
        return ReturnJson.success();
    }

}
