package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.StepAuditConfig;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface StepAuditConfigMapper {

    int deleteStepAuditConfig(List<Integer> idList);

    int insertStepAuditConfig(StepAuditConfig record);

    List<StepAuditConfig> selectStepAuditConfig(StepAuditConfig example);

    int updateStepAuditConfig(StepAuditConfig record);

    /**
     * 根据产品id 项目id 渠道 账号确定唯一值
     */
    @Select("select id from xyxtj.step_audit_config " +
            "where appid = #{appid} and pid = #{pid} and cha = #{cha} and account = #{account} and amount = #{amount} limit 1 ")
    Integer selectConfigByAppIdLinPidLinChaLinAccount(StepAuditConfig stepAuditConfig);



}