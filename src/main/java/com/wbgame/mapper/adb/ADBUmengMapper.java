package com.wbgame.mapper.adb;

import com.wbgame.pojo.*;
import com.wbgame.pojo.mobile.ErrorCodeReportLine;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface ADBUmengMapper {

    /**
     * 批量插入渠道分布
     * @param channels
     * @return
     */
    int batchInsertUserChannelTotal(List<UmengUserChannelTotal> channels);

    /**
     * 批量插入渠道分布，主键重复删除后插入
     * @param channels 需要新增的数据
     * @return 影响行数
     */
    int replaceIntoUserChannelTotal(@Param("channels") List<UmengUserChannelTotal> channels);

    List<Map<String, Object>> selectUmengBaseReport(Map<String, Object> param);

    Map<String, Object> countUmengBaseReport(Map param);

    List<String> selectDeviceReport();

    List<AdvEvent> selectAdvSQL(Map param);

    List<Map<String, Object>> selectAdvAppInOuttReport(Map param);

    List<AdvEvent> selectAdvEventHead(Map param);

    List<Map<String, Object>> selectAdvEventReport(Map param);

    List<AdvEvent> selectAdvSQL2(Map param);

    List<Map<String, Object>> selectAdvEventReport2(Map param);

    List<AdvEvent> selectAdvEventHead2(Map param);

    List<Map<String, Object>> selectAppLockReport(Map param);

    List<ProdutChannelDataVo> selectDauUserByChannel(Map<String, Object> map);

    List<Map<String,Object>> getChannelsAndVersions(@Param("appid") String appid);

    List<Map<String, Object>> selectAdvertKeeps(Map<String,Object> param);

    List<Map<String, Object>> selectMonthChannelTotal(Map param);

    List<Map<String, Object>> selectBaiduActRate(Map param);

    List<Map<String, Object>> selectUmengOverseaReport(Map<String, Object> param);

    Map<String, Object> countUmengOverseaReport(Map param);

    @Select("SELECT DISTINCT country FROM ads_umeng_custom_total_daily_oversea ORDER BY country DESC")
    List<String> listCountry();
}
