package com.wbgame.mapper.master.jettison;


import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.wbgame.pojo.common.param.CreateOfflineFileParam;
import com.wbgame.pojo.common.vo.OfflineFileTaskVo;
import com.wbgame.pojo.jettison.TaskCenterDto;
import com.wbgame.pojo.jettison.vo.TaskDetailVo;


@Repository
public interface TaskCenterMapper {
	
	public List<TaskCenterDto> taskList(TaskCenterDto vo)throws Exception;
	
	public List<TaskDetailVo> getDetail(@Param("batchId")int batchId)throws Exception;
	 
	int saveOfflineFileTask (CreateOfflineFileParam param);
	
	int updateOfflineFileTaskStatus (CreateOfflineFileParam param);
	
	public List<OfflineFileTaskVo> offlineFileTaskList(OfflineFileTaskVo vo);
	
	int deleteOfflineFileTask (OfflineFileTaskVo param);
	
}
