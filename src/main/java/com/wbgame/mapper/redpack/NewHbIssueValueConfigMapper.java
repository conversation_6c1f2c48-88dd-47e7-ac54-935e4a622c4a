package com.wbgame.mapper.redpack;

import com.wbgame.pojo.HbIssueValueConfig;

import java.util.List;

public interface NewHbIssueValueConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(HbIssueValueConfig record);

    int insertSelective(HbIssueValueConfig record);

    HbIssueValueConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(HbIssueValueConfig record);

    int updateByPrimaryKey(HbIssueValueConfig record);

    List<HbIssueValueConfig> selectByAll(HbIssueValueConfig hbIssueValueConfig);

    List<HbIssueValueConfig> selectByPid(String pid);

    void batchInsertHbIssueValueConfigs(List<HbIssueValueConfig> hbIssueValueConfigs);
}