-- 邮件验证码管理表
CREATE TABLE `dn_email_verification_code` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `email` varchar(100) NOT NULL COMMENT '验证邮箱',
  `message_id` varchar(100) NOT NULL COMMENT '邮件ID，唯一标识',
  `subject` varchar(200) DEFAULT NULL COMMENT '邮件标题',
  `content` text COMMENT '邮件内容',
  `verification_code` varchar(20) DEFAULT NULL COMMENT '解析出的验证码',
  `platform` varchar(50) DEFAULT NULL COMMENT '平台：华为/小米/荣耀',
  `account_info` varchar(200) DEFAULT NULL COMMENT '账号信息：如华为账号、荣耀账号、小米用户等',
  `internal_date` varchar(200) DEFAULT NULL COMMENT '事件时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_email` (`email`),
  KEY `idx_platform` (`platform`),
  KEY `idx_account_info` (`account_info`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件验证码管理表';
