<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.DnActiveAddSummaryMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.advert.AddActiveSummary">
        <id column="tdate" property="tdate" jdbcType="DATE"/>
        <id column="appid" property="appid" jdbcType="VARCHAR"/>
        <id column="pid" property="pid" jdbcType="VARCHAR"/>
        <id column="cha" property="cha" jdbcType="VARCHAR"/>
        <id column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="cha_id" property="chaId" jdbcType="INTEGER"/>
        <result column="pid" property="pid" jdbcType="INTEGER"/>
        <result column="cha_media" property="chaMedia" jdbcType="VARCHAR"/>
        <result column="cha_sub_launch" property="chaSubLaunch" jdbcType="VARCHAR"/>
        <result column="type_name" property="typeName" jdbcType="VARCHAR"/>
        <result column="new_users" property="newUsers" jdbcType="INTEGER"/>
        <result column="act_users" property="actUsers" jdbcType="INTEGER"/>
        <result column="type_name" property="typeName" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        tdate
        , appid, pid, cha, platform, version, cha_id, cha_media, cha_sub_launch, type_name,
    new_users, act_users
    </sql>

    <insert id="insert" parameterType="com.wbgame.pojo.advert.AddActiveSummary">

        <foreach collection="list" item="data" separator=";">
            replace
            into dn_active_add_summary (tdate, appid, pid,
            cha, platform, version,
            cha_id, cha_media, cha_sub_launch,
            type_name, new_users, act_users
            )
            values (
            #{data.tdate,jdbcType=DATE},
            #{data.appid,jdbcType=VARCHAR},
            #{data.pid,jdbcType=VARCHAR},
            #{data.cha,jdbcType=VARCHAR},
            #{data.platform,jdbcType=VARCHAR},
            #{data.version,jdbcType=VARCHAR},
            #{data.chaId,jdbcType=INTEGER},
            #{data.chaMedia,jdbcType=VARCHAR},
            #{data.chaSubLaunch,jdbcType=VARCHAR},
            #{data.typeName,jdbcType=VARCHAR},
            #{data.newUsers,jdbcType=INTEGER},
            #{data.actUsers,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <select id="selectActiveAddSummary" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.advert.AddActiveSummary" >

        select

            <if test="group != null and group != ''">
                ${group},
            </if>
            version,
            cha_id,
            cha_media,
            cha_sub_launch,
            concat(type_name, '-', cha_id) type_name,
            sum(new_users) new_users,
            sum(act_users) act_users

        from (

            <include refid="select1"/>
                 ) a
        group by
                <if test="group != null and group != ''">
                    ${group}
                </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>

                order by tdate
            </otherwise>
        </choose>


    </select>

    <select id="countActiveAddSummary" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.advert.AddActiveSummary" >

        select
               sum(new_users) new_users,
               sum(act_users) act_users
        from (


        select

        <if test="group != null and group != ''">
            ${group},
        </if>
        cha,
        version,
        platform,
        cha_id,
        cha_media,
        cha_sub_launch,
        type_name,
        sum(new_users) new_users,
        sum(act_users) act_users

        from (

        <include refid="select1"/>
        ) a
        group by

        cha,
        version,
        platform,
        cha_id,
        cha_media,
        cha_sub_launch,
        type_name
        <if test="group != null and group != ''">
            ,${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>

                order by tdate
            </otherwise>
        </choose>

                          ) a


    </select>

    <sql id="select1">

        select
               <choose>

                   <when test="summary == 2">

                       date_format(tdate, '%x-%v') tdate,
                   </when>
                   <when test="summary == 3">
                       date_format(tdate, '%Y-%m') tdate,
                   </when>
                   <when test="summary == 4">
                       date_format(tdate, '%Y') tdate,
                   </when>
                    <otherwise>
                        tdate,
                    </otherwise>
               </choose>


               appid,
               cha,
               pid,
               version,
               platform,
               cha_id,
               cha_media,
               cha_sub_launch,
               type_name,
               new_users,
               act_users

        from dn_active_add_summary
        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and tdate between #{start_date} and #{end_date}
            </if>

            <if test="chaSubLaunchList != null and chaSubLaunchList.size > 0">
                AND cha_sub_launch IN
                <foreach collection="chaSubLaunchList" item="s" open="(" separator="," close=")">
                    #{s}
                </foreach>
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND cha IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND pid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>
            <if test="mediaList != null and mediaList.size > 0">
                AND cha_media IN
                <foreach collection="mediaList" item="m" open="(" separator="," close=")">
                    #{m}
                </foreach>
            </if>
			<if test="chaId != null and chaId != ''">
				and cha_id in (${chaId})
			</if>

        </where>


    </sql>
</mapper>